'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('../../dist/triangle-9e5a8229.cjs.prod.js');
require('three');
require('../../dist/matrix-e0b2acc5.cjs.prod.js');
var misc_dist_maathMisc = require('../../dist/misc-8dab750e.cjs.prod.js');
require('../../dist/isNativeReflectConstruct-9acebf01.cjs.prod.js');



exports.clamp = misc_dist_maathMisc.clamp;
exports.convexHull = misc_dist_maathMisc.convexHull;
exports.coordinateToPoint = misc_dist_maathMisc.coordinateToPoint;
exports.degToRad = misc_dist_maathMisc.degToRad;
exports.deltaAngle = misc_dist_maathMisc.deltaAngle;
exports.fade = misc_dist_maathMisc.fade;
exports.fibonacciOnSphere = misc_dist_maathMisc.fibonacciOnSphere;
exports.get2DFromIndex = misc_dist_maathMisc.get2DFromIndex;
exports.get3DFromIndex = misc_dist_maathMisc.get3DFromIndex;
exports.getIndexFrom2D = misc_dist_maathMisc.getIndexFrom2D;
exports.getIndexFrom3D = misc_dist_maathMisc.getIndexFrom3D;
exports.inverseLerp = misc_dist_maathMisc.inverseLerp;
exports.lerp = misc_dist_maathMisc.lerp;
exports.lexicographic = misc_dist_maathMisc.lexicographic;
exports.normalize = misc_dist_maathMisc.normalize;
exports.planeSegmentIntersection = misc_dist_maathMisc.planeSegmentIntersection;
exports.pointOnCubeToPointOnSphere = misc_dist_maathMisc.pointOnCubeToPointOnSphere;
exports.pointToCoordinate = misc_dist_maathMisc.pointToCoordinate;
exports.pointToPlaneDistance = misc_dist_maathMisc.pointToPlaneDistance;
exports.radToDeg = misc_dist_maathMisc.radToDeg;
exports.remap = misc_dist_maathMisc.remap;
exports.rotateVectorOnVector = misc_dist_maathMisc.rotateVectorOnVector;
exports.vectorEquals = misc_dist_maathMisc.vectorEquals;
