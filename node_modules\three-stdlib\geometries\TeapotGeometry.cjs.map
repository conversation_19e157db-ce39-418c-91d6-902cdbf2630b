{"version": 3, "file": "TeapotGeometry.cjs", "sources": ["../../src/geometries/TeapotGeometry.js"], "sourcesContent": ["import { <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>, BufferGeometry, Matrix4, Vector3, Vector4 } from 'three'\n\n/**\n * Tessellates the famous Utah teapot database by <PERSON> into triangles.\n *\n * Parameters: size = 50, segments = 10, bottom = true, lid = true, body = true,\n *   fitLid = false, blinn = true\n *\n * size is a relative scale: I've scaled the teapot to fit vertically between -1 and 1.\n * Think of it as a \"radius\".\n * segments - number of line segments to subdivide each patch edge;\n *   1 is possible but gives degenerates, so two is the real minimum.\n * bottom - boolean, if true (default) then the bottom patches are added. Some consider\n *   adding the bottom heresy, so set this to \"false\" to adhere to the One True Way.\n * lid - to remove the lid and look inside, set to true.\n * body - to remove the body and leave the lid, set this and \"bottom\" to false.\n * fitLid - the lid is a tad small in the original. This stretches it a bit so you can't\n *   see the teapot's insides through the gap.\n * blinn - <PERSON> scaled the original data vertically by dividing by about 1.3 to look\n *   nicer. If you want to see the original teapot, similar to the real-world model, set\n *   this to false. True by default.\n *   See http://en.wikipedia.org/wiki/File:Original_Utah_Teapot.jpg for the original\n *   real-world teapot (from http://en.wikipedia.org/wiki/Utah_teapot).\n *\n * Note that the bottom (the last four patches) is not flat - blame <PERSON>, not me.\n *\n * The teapot should normally be rendered as a double sided object, since for some\n * patches both sides can be seen, e.g., the gap around the lid and inside the spout.\n *\n * Segments 'n' determines the number of triangles output.\n *   Total triangles = 32*2*n*n - 8*n    [degenerates at the top and bottom cusps are deleted]\n *\n *   size_factor   # triangles\n *       1          56\n *       2         240\n *       3         552\n *       4         992\n *\n *      10        6320\n *      20       25440\n *      30       57360\n *\n * Code converted from my ancient SPD software, http://tog.acm.org/resources/SPD/\n * Created for the Udacity course \"Interactive Rendering\", http://bit.ly/ericity\n * Lesson: https://www.udacity.com/course/viewer#!/c-cs291/l-68866048/m-106482448\n * YouTube video on teapot history: https://www.youtube.com/watch?v=DxMfblPzFNc\n *\n * See https://en.wikipedia.org/wiki/Utah_teapot for the history of the teapot\n *\n */\n\nclass TeapotGeometry extends BufferGeometry {\n  constructor(size, segments, bottom, lid, body, fitLid, blinn) {\n    // 32 * 4 * 4 Bezier spline patches\n    const teapotPatches = [\n      /*rim*/\n      0,\n      1,\n      2,\n      3,\n      4,\n      5,\n      6,\n      7,\n      8,\n      9,\n      10,\n      11,\n      12,\n      13,\n      14,\n      15,\n      3,\n      16,\n      17,\n      18,\n      7,\n      19,\n      20,\n      21,\n      11,\n      22,\n      23,\n      24,\n      15,\n      25,\n      26,\n      27,\n      18,\n      28,\n      29,\n      30,\n      21,\n      31,\n      32,\n      33,\n      24,\n      34,\n      35,\n      36,\n      27,\n      37,\n      38,\n      39,\n      30,\n      40,\n      41,\n      0,\n      33,\n      42,\n      43,\n      4,\n      36,\n      44,\n      45,\n      8,\n      39,\n      46,\n      47,\n      12,\n      /*body*/\n      12,\n      13,\n      14,\n      15,\n      48,\n      49,\n      50,\n      51,\n      52,\n      53,\n      54,\n      55,\n      56,\n      57,\n      58,\n      59,\n      15,\n      25,\n      26,\n      27,\n      51,\n      60,\n      61,\n      62,\n      55,\n      63,\n      64,\n      65,\n      59,\n      66,\n      67,\n      68,\n      27,\n      37,\n      38,\n      39,\n      62,\n      69,\n      70,\n      71,\n      65,\n      72,\n      73,\n      74,\n      68,\n      75,\n      76,\n      77,\n      39,\n      46,\n      47,\n      12,\n      71,\n      78,\n      79,\n      48,\n      74,\n      80,\n      81,\n      52,\n      77,\n      82,\n      83,\n      56,\n      56,\n      57,\n      58,\n      59,\n      84,\n      85,\n      86,\n      87,\n      88,\n      89,\n      90,\n      91,\n      92,\n      93,\n      94,\n      95,\n      59,\n      66,\n      67,\n      68,\n      87,\n      96,\n      97,\n      98,\n      91,\n      99,\n      100,\n      101,\n      95,\n      102,\n      103,\n      104,\n      68,\n      75,\n      76,\n      77,\n      98,\n      105,\n      106,\n      107,\n      101,\n      108,\n      109,\n      110,\n      104,\n      111,\n      112,\n      113,\n      77,\n      82,\n      83,\n      56,\n      107,\n      114,\n      115,\n      84,\n      110,\n      116,\n      117,\n      88,\n      113,\n      118,\n      119,\n      92,\n      /*handle*/\n      120,\n      121,\n      122,\n      123,\n      124,\n      125,\n      126,\n      127,\n      128,\n      129,\n      130,\n      131,\n      132,\n      133,\n      134,\n      135,\n      123,\n      136,\n      137,\n      120,\n      127,\n      138,\n      139,\n      124,\n      131,\n      140,\n      141,\n      128,\n      135,\n      142,\n      143,\n      132,\n      132,\n      133,\n      134,\n      135,\n      144,\n      145,\n      146,\n      147,\n      148,\n      149,\n      150,\n      151,\n      68,\n      152,\n      153,\n      154,\n      135,\n      142,\n      143,\n      132,\n      147,\n      155,\n      156,\n      144,\n      151,\n      157,\n      158,\n      148,\n      154,\n      159,\n      160,\n      68,\n      /*spout*/\n      161,\n      162,\n      163,\n      164,\n      165,\n      166,\n      167,\n      168,\n      169,\n      170,\n      171,\n      172,\n      173,\n      174,\n      175,\n      176,\n      164,\n      177,\n      178,\n      161,\n      168,\n      179,\n      180,\n      165,\n      172,\n      181,\n      182,\n      169,\n      176,\n      183,\n      184,\n      173,\n      173,\n      174,\n      175,\n      176,\n      185,\n      186,\n      187,\n      188,\n      189,\n      190,\n      191,\n      192,\n      193,\n      194,\n      195,\n      196,\n      176,\n      183,\n      184,\n      173,\n      188,\n      197,\n      198,\n      185,\n      192,\n      199,\n      200,\n      189,\n      196,\n      201,\n      202,\n      193,\n      /*lid*/\n      203,\n      203,\n      203,\n      203,\n      204,\n      205,\n      206,\n      207,\n      208,\n      208,\n      208,\n      208,\n      209,\n      210,\n      211,\n      212,\n      203,\n      203,\n      203,\n      203,\n      207,\n      213,\n      214,\n      215,\n      208,\n      208,\n      208,\n      208,\n      212,\n      216,\n      217,\n      218,\n      203,\n      203,\n      203,\n      203,\n      215,\n      219,\n      220,\n      221,\n      208,\n      208,\n      208,\n      208,\n      218,\n      222,\n      223,\n      224,\n      203,\n      203,\n      203,\n      203,\n      221,\n      225,\n      226,\n      204,\n      208,\n      208,\n      208,\n      208,\n      224,\n      227,\n      228,\n      209,\n      209,\n      210,\n      211,\n      212,\n      229,\n      230,\n      231,\n      232,\n      233,\n      234,\n      235,\n      236,\n      237,\n      238,\n      239,\n      240,\n      212,\n      216,\n      217,\n      218,\n      232,\n      241,\n      242,\n      243,\n      236,\n      244,\n      245,\n      246,\n      240,\n      247,\n      248,\n      249,\n      218,\n      222,\n      223,\n      224,\n      243,\n      250,\n      251,\n      252,\n      246,\n      253,\n      254,\n      255,\n      249,\n      256,\n      257,\n      258,\n      224,\n      227,\n      228,\n      209,\n      252,\n      259,\n      260,\n      229,\n      255,\n      261,\n      262,\n      233,\n      258,\n      263,\n      264,\n      237,\n      /*bottom*/\n      265,\n      265,\n      265,\n      265,\n      266,\n      267,\n      268,\n      269,\n      270,\n      271,\n      272,\n      273,\n      92,\n      119,\n      118,\n      113,\n      265,\n      265,\n      265,\n      265,\n      269,\n      274,\n      275,\n      276,\n      273,\n      277,\n      278,\n      279,\n      113,\n      112,\n      111,\n      104,\n      265,\n      265,\n      265,\n      265,\n      276,\n      280,\n      281,\n      282,\n      279,\n      283,\n      284,\n      285,\n      104,\n      103,\n      102,\n      95,\n      265,\n      265,\n      265,\n      265,\n      282,\n      286,\n      287,\n      266,\n      285,\n      288,\n      289,\n      270,\n      95,\n      94,\n      93,\n      92,\n    ]\n\n    const teapotVertices = [\n      1.4,\n      0,\n      2.4,\n      1.4,\n      -0.784,\n      2.4,\n      0.784,\n      -1.4,\n      2.4,\n      0,\n      -1.4,\n      2.4,\n      1.3375,\n      0,\n      2.53125,\n      1.3375,\n      -0.749,\n      2.53125,\n      0.749,\n      -1.3375,\n      2.53125,\n      0,\n      -1.3375,\n      2.53125,\n      1.4375,\n      0,\n      2.53125,\n      1.4375,\n      -0.805,\n      2.53125,\n      0.805,\n      -1.4375,\n      2.53125,\n      0,\n      -1.4375,\n      2.53125,\n      1.5,\n      0,\n      2.4,\n      1.5,\n      -0.84,\n      2.4,\n      0.84,\n      -1.5,\n      2.4,\n      0,\n      -1.5,\n      2.4,\n      -0.784,\n      -1.4,\n      2.4,\n      -1.4,\n      -0.784,\n      2.4,\n      -1.4,\n      0,\n      2.4,\n      -0.749,\n      -1.3375,\n      2.53125,\n      -1.3375,\n      -0.749,\n      2.53125,\n      -1.3375,\n      0,\n      2.53125,\n      -0.805,\n      -1.4375,\n      2.53125,\n      -1.4375,\n      -0.805,\n      2.53125,\n      -1.4375,\n      0,\n      2.53125,\n      -0.84,\n      -1.5,\n      2.4,\n      -1.5,\n      -0.84,\n      2.4,\n      -1.5,\n      0,\n      2.4,\n      -1.4,\n      0.784,\n      2.4,\n      -0.784,\n      1.4,\n      2.4,\n      0,\n      1.4,\n      2.4,\n      -1.3375,\n      0.749,\n      2.53125,\n      -0.749,\n      1.3375,\n      2.53125,\n      0,\n      1.3375,\n      2.53125,\n      -1.4375,\n      0.805,\n      2.53125,\n      -0.805,\n      1.4375,\n      2.53125,\n      0,\n      1.4375,\n      2.53125,\n      -1.5,\n      0.84,\n      2.4,\n      -0.84,\n      1.5,\n      2.4,\n      0,\n      1.5,\n      2.4,\n      0.784,\n      1.4,\n      2.4,\n      1.4,\n      0.784,\n      2.4,\n      0.749,\n      1.3375,\n      2.53125,\n      1.3375,\n      0.749,\n      2.53125,\n      0.805,\n      1.4375,\n      2.53125,\n      1.4375,\n      0.805,\n      2.53125,\n      0.84,\n      1.5,\n      2.4,\n      1.5,\n      0.84,\n      2.4,\n      1.75,\n      0,\n      1.875,\n      1.75,\n      -0.98,\n      1.875,\n      0.98,\n      -1.75,\n      1.875,\n      0,\n      -1.75,\n      1.875,\n      2,\n      0,\n      1.35,\n      2,\n      -1.12,\n      1.35,\n      1.12,\n      -2,\n      1.35,\n      0,\n      -2,\n      1.35,\n      2,\n      0,\n      0.9,\n      2,\n      -1.12,\n      0.9,\n      1.12,\n      -2,\n      0.9,\n      0,\n      -2,\n      0.9,\n      -0.98,\n      -1.75,\n      1.875,\n      -1.75,\n      -0.98,\n      1.875,\n      -1.75,\n      0,\n      1.875,\n      -1.12,\n      -2,\n      1.35,\n      -2,\n      -1.12,\n      1.35,\n      -2,\n      0,\n      1.35,\n      -1.12,\n      -2,\n      0.9,\n      -2,\n      -1.12,\n      0.9,\n      -2,\n      0,\n      0.9,\n      -1.75,\n      0.98,\n      1.875,\n      -0.98,\n      1.75,\n      1.875,\n      0,\n      1.75,\n      1.875,\n      -2,\n      1.12,\n      1.35,\n      -1.12,\n      2,\n      1.35,\n      0,\n      2,\n      1.35,\n      -2,\n      1.12,\n      0.9,\n      -1.12,\n      2,\n      0.9,\n      0,\n      2,\n      0.9,\n      0.98,\n      1.75,\n      1.875,\n      1.75,\n      0.98,\n      1.875,\n      1.12,\n      2,\n      1.35,\n      2,\n      1.12,\n      1.35,\n      1.12,\n      2,\n      0.9,\n      2,\n      1.12,\n      0.9,\n      2,\n      0,\n      0.45,\n      2,\n      -1.12,\n      0.45,\n      1.12,\n      -2,\n      0.45,\n      0,\n      -2,\n      0.45,\n      1.5,\n      0,\n      0.225,\n      1.5,\n      -0.84,\n      0.225,\n      0.84,\n      -1.5,\n      0.225,\n      0,\n      -1.5,\n      0.225,\n      1.5,\n      0,\n      0.15,\n      1.5,\n      -0.84,\n      0.15,\n      0.84,\n      -1.5,\n      0.15,\n      0,\n      -1.5,\n      0.15,\n      -1.12,\n      -2,\n      0.45,\n      -2,\n      -1.12,\n      0.45,\n      -2,\n      0,\n      0.45,\n      -0.84,\n      -1.5,\n      0.225,\n      -1.5,\n      -0.84,\n      0.225,\n      -1.5,\n      0,\n      0.225,\n      -0.84,\n      -1.5,\n      0.15,\n      -1.5,\n      -0.84,\n      0.15,\n      -1.5,\n      0,\n      0.15,\n      -2,\n      1.12,\n      0.45,\n      -1.12,\n      2,\n      0.45,\n      0,\n      2,\n      0.45,\n      -1.5,\n      0.84,\n      0.225,\n      -0.84,\n      1.5,\n      0.225,\n      0,\n      1.5,\n      0.225,\n      -1.5,\n      0.84,\n      0.15,\n      -0.84,\n      1.5,\n      0.15,\n      0,\n      1.5,\n      0.15,\n      1.12,\n      2,\n      0.45,\n      2,\n      1.12,\n      0.45,\n      0.84,\n      1.5,\n      0.225,\n      1.5,\n      0.84,\n      0.225,\n      0.84,\n      1.5,\n      0.15,\n      1.5,\n      0.84,\n      0.15,\n      -1.6,\n      0,\n      2.025,\n      -1.6,\n      -0.3,\n      2.025,\n      -1.5,\n      -0.3,\n      2.25,\n      -1.5,\n      0,\n      2.25,\n      -2.3,\n      0,\n      2.025,\n      -2.3,\n      -0.3,\n      2.025,\n      -2.5,\n      -0.3,\n      2.25,\n      -2.5,\n      0,\n      2.25,\n      -2.7,\n      0,\n      2.025,\n      -2.7,\n      -0.3,\n      2.025,\n      -3,\n      -0.3,\n      2.25,\n      -3,\n      0,\n      2.25,\n      -2.7,\n      0,\n      1.8,\n      -2.7,\n      -0.3,\n      1.8,\n      -3,\n      -0.3,\n      1.8,\n      -3,\n      0,\n      1.8,\n      -1.5,\n      0.3,\n      2.25,\n      -1.6,\n      0.3,\n      2.025,\n      -2.5,\n      0.3,\n      2.25,\n      -2.3,\n      0.3,\n      2.025,\n      -3,\n      0.3,\n      2.25,\n      -2.7,\n      0.3,\n      2.025,\n      -3,\n      0.3,\n      1.8,\n      -2.7,\n      0.3,\n      1.8,\n      -2.7,\n      0,\n      1.575,\n      -2.7,\n      -0.3,\n      1.575,\n      -3,\n      -0.3,\n      1.35,\n      -3,\n      0,\n      1.35,\n      -2.5,\n      0,\n      1.125,\n      -2.5,\n      -0.3,\n      1.125,\n      -2.65,\n      -0.3,\n      0.9375,\n      -2.65,\n      0,\n      0.9375,\n      -2,\n      -0.3,\n      0.9,\n      -1.9,\n      -0.3,\n      0.6,\n      -1.9,\n      0,\n      0.6,\n      -3,\n      0.3,\n      1.35,\n      -2.7,\n      0.3,\n      1.575,\n      -2.65,\n      0.3,\n      0.9375,\n      -2.5,\n      0.3,\n      1.125,\n      -1.9,\n      0.3,\n      0.6,\n      -2,\n      0.3,\n      0.9,\n      1.7,\n      0,\n      1.425,\n      1.7,\n      -0.66,\n      1.425,\n      1.7,\n      -0.66,\n      0.6,\n      1.7,\n      0,\n      0.6,\n      2.6,\n      0,\n      1.425,\n      2.6,\n      -0.66,\n      1.425,\n      3.1,\n      -0.66,\n      0.825,\n      3.1,\n      0,\n      0.825,\n      2.3,\n      0,\n      2.1,\n      2.3,\n      -0.25,\n      2.1,\n      2.4,\n      -0.25,\n      2.025,\n      2.4,\n      0,\n      2.025,\n      2.7,\n      0,\n      2.4,\n      2.7,\n      -0.25,\n      2.4,\n      3.3,\n      -0.25,\n      2.4,\n      3.3,\n      0,\n      2.4,\n      1.7,\n      0.66,\n      0.6,\n      1.7,\n      0.66,\n      1.425,\n      3.1,\n      0.66,\n      0.825,\n      2.6,\n      0.66,\n      1.425,\n      2.4,\n      0.25,\n      2.025,\n      2.3,\n      0.25,\n      2.1,\n      3.3,\n      0.25,\n      2.4,\n      2.7,\n      0.25,\n      2.4,\n      2.8,\n      0,\n      2.475,\n      2.8,\n      -0.25,\n      2.475,\n      3.525,\n      -0.25,\n      2.49375,\n      3.525,\n      0,\n      2.49375,\n      2.9,\n      0,\n      2.475,\n      2.9,\n      -0.15,\n      2.475,\n      3.45,\n      -0.15,\n      2.5125,\n      3.45,\n      0,\n      2.5125,\n      2.8,\n      0,\n      2.4,\n      2.8,\n      -0.15,\n      2.4,\n      3.2,\n      -0.15,\n      2.4,\n      3.2,\n      0,\n      2.4,\n      3.525,\n      0.25,\n      2.49375,\n      2.8,\n      0.25,\n      2.475,\n      3.45,\n      0.15,\n      2.5125,\n      2.9,\n      0.15,\n      2.475,\n      3.2,\n      0.15,\n      2.4,\n      2.8,\n      0.15,\n      2.4,\n      0,\n      0,\n      3.15,\n      0.8,\n      0,\n      3.15,\n      0.8,\n      -0.45,\n      3.15,\n      0.45,\n      -0.8,\n      3.15,\n      0,\n      -0.8,\n      3.15,\n      0,\n      0,\n      2.85,\n      0.2,\n      0,\n      2.7,\n      0.2,\n      -0.112,\n      2.7,\n      0.112,\n      -0.2,\n      2.7,\n      0,\n      -0.2,\n      2.7,\n      -0.45,\n      -0.8,\n      3.15,\n      -0.8,\n      -0.45,\n      3.15,\n      -0.8,\n      0,\n      3.15,\n      -0.112,\n      -0.2,\n      2.7,\n      -0.2,\n      -0.112,\n      2.7,\n      -0.2,\n      0,\n      2.7,\n      -0.8,\n      0.45,\n      3.15,\n      -0.45,\n      0.8,\n      3.15,\n      0,\n      0.8,\n      3.15,\n      -0.2,\n      0.112,\n      2.7,\n      -0.112,\n      0.2,\n      2.7,\n      0,\n      0.2,\n      2.7,\n      0.45,\n      0.8,\n      3.15,\n      0.8,\n      0.45,\n      3.15,\n      0.112,\n      0.2,\n      2.7,\n      0.2,\n      0.112,\n      2.7,\n      0.4,\n      0,\n      2.55,\n      0.4,\n      -0.224,\n      2.55,\n      0.224,\n      -0.4,\n      2.55,\n      0,\n      -0.4,\n      2.55,\n      1.3,\n      0,\n      2.55,\n      1.3,\n      -0.728,\n      2.55,\n      0.728,\n      -1.3,\n      2.55,\n      0,\n      -1.3,\n      2.55,\n      1.3,\n      0,\n      2.4,\n      1.3,\n      -0.728,\n      2.4,\n      0.728,\n      -1.3,\n      2.4,\n      0,\n      -1.3,\n      2.4,\n      -0.224,\n      -0.4,\n      2.55,\n      -0.4,\n      -0.224,\n      2.55,\n      -0.4,\n      0,\n      2.55,\n      -0.728,\n      -1.3,\n      2.55,\n      -1.3,\n      -0.728,\n      2.55,\n      -1.3,\n      0,\n      2.55,\n      -0.728,\n      -1.3,\n      2.4,\n      -1.3,\n      -0.728,\n      2.4,\n      -1.3,\n      0,\n      2.4,\n      -0.4,\n      0.224,\n      2.55,\n      -0.224,\n      0.4,\n      2.55,\n      0,\n      0.4,\n      2.55,\n      -1.3,\n      0.728,\n      2.55,\n      -0.728,\n      1.3,\n      2.55,\n      0,\n      1.3,\n      2.55,\n      -1.3,\n      0.728,\n      2.4,\n      -0.728,\n      1.3,\n      2.4,\n      0,\n      1.3,\n      2.4,\n      0.224,\n      0.4,\n      2.55,\n      0.4,\n      0.224,\n      2.55,\n      0.728,\n      1.3,\n      2.55,\n      1.3,\n      0.728,\n      2.55,\n      0.728,\n      1.3,\n      2.4,\n      1.3,\n      0.728,\n      2.4,\n      0,\n      0,\n      0,\n      1.425,\n      0,\n      0,\n      1.425,\n      0.798,\n      0,\n      0.798,\n      1.425,\n      0,\n      0,\n      1.425,\n      0,\n      1.5,\n      0,\n      0.075,\n      1.5,\n      0.84,\n      0.075,\n      0.84,\n      1.5,\n      0.075,\n      0,\n      1.5,\n      0.075,\n      -0.798,\n      1.425,\n      0,\n      -1.425,\n      0.798,\n      0,\n      -1.425,\n      0,\n      0,\n      -0.84,\n      1.5,\n      0.075,\n      -1.5,\n      0.84,\n      0.075,\n      -1.5,\n      0,\n      0.075,\n      -1.425,\n      -0.798,\n      0,\n      -0.798,\n      -1.425,\n      0,\n      0,\n      -1.425,\n      0,\n      -1.5,\n      -0.84,\n      0.075,\n      -0.84,\n      -1.5,\n      0.075,\n      0,\n      -1.5,\n      0.075,\n      0.798,\n      -1.425,\n      0,\n      1.425,\n      -0.798,\n      0,\n      0.84,\n      -1.5,\n      0.075,\n      1.5,\n      -0.84,\n      0.075,\n    ]\n\n    super()\n\n    size = size || 50\n\n    // number of segments per patch\n    segments = segments !== undefined ? Math.max(2, Math.floor(segments) || 10) : 10\n\n    // which parts should be visible\n    bottom = bottom === undefined ? true : bottom\n    lid = lid === undefined ? true : lid\n    body = body === undefined ? true : body\n\n    // Should the lid be snug? It's not traditional, but we make it snug by default\n    fitLid = fitLid === undefined ? true : fitLid\n\n    // Jim Blinn scaled the teapot down in size by about 1.3 for\n    // some rendering tests. He liked the new proportions that he kept\n    // the data in this form. The model was distributed with these new\n    // proportions and became the norm. Trivia: comparing images of the\n    // real teapot and the computer model, the ratio for the bowl of the\n    // real teapot is more like 1.25, but since 1.3 is the traditional\n    // value given, we use it here.\n    const blinnScale = 1.3\n    blinn = blinn === undefined ? true : blinn\n\n    // scale the size to be the real scaling factor\n    const maxHeight = 3.15 * (blinn ? 1 : blinnScale)\n\n    const maxHeight2 = maxHeight / 2\n    const trueSize = size / maxHeight2\n\n    // Number of elements depends on what is needed. Subtract degenerate\n    // triangles at tip of bottom and lid out in advance.\n    let numTriangles = bottom ? (8 * segments - 4) * segments : 0\n    numTriangles += lid ? (16 * segments - 4) * segments : 0\n    numTriangles += body ? 40 * segments * segments : 0\n\n    const indices = new Uint32Array(numTriangles * 3)\n\n    let numVertices = bottom ? 4 : 0\n    numVertices += lid ? 8 : 0\n    numVertices += body ? 20 : 0\n    numVertices *= (segments + 1) * (segments + 1)\n\n    const vertices = new Float32Array(numVertices * 3)\n    const normals = new Float32Array(numVertices * 3)\n    const uvs = new Float32Array(numVertices * 2)\n\n    // Bezier form\n    const ms = new Matrix4()\n    ms.set(-1.0, 3.0, -3.0, 1.0, 3.0, -6.0, 3.0, 0.0, -3.0, 3.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0)\n\n    const g = []\n    let i, r, c\n\n    const sp = []\n    const tp = []\n    const dsp = []\n    const dtp = []\n\n    // M * G * M matrix, sort of see\n    // http://www.cs.helsinki.fi/group/goa/mallinnus/curves/surfaces.html\n    const mgm = []\n\n    const vert = []\n    const sdir = []\n    const tdir = []\n\n    const norm = new Vector3()\n\n    let tcoord\n\n    let sstep, tstep\n    let vertPerRow\n\n    let s, t, sval, tval, p\n    let dsval = 0\n    let dtval = 0\n\n    const normOut = new Vector3()\n    let v1, v2, v3, v4\n\n    const gmx = new Matrix4()\n    const tmtx = new Matrix4()\n\n    const vsp = new Vector4()\n    const vtp = new Vector4()\n    const vdsp = new Vector4()\n    const vdtp = new Vector4()\n\n    const vsdir = new Vector3()\n    const vtdir = new Vector3()\n\n    const mst = ms.clone()\n    mst.transpose()\n\n    // internal function: test if triangle has any matching vertices;\n    // if so, don't save triangle, since it won't display anything.\n    const notDegenerate = (\n      vtx1,\n      vtx2,\n      vtx3, // if any vertex matches, return false\n    ) =>\n      !(\n        (vertices[vtx1 * 3] === vertices[vtx2 * 3] &&\n          vertices[vtx1 * 3 + 1] === vertices[vtx2 * 3 + 1] &&\n          vertices[vtx1 * 3 + 2] === vertices[vtx2 * 3 + 2]) ||\n        (vertices[vtx1 * 3] === vertices[vtx3 * 3] &&\n          vertices[vtx1 * 3 + 1] === vertices[vtx3 * 3 + 1] &&\n          vertices[vtx1 * 3 + 2] === vertices[vtx3 * 3 + 2]) ||\n        (vertices[vtx2 * 3] === vertices[vtx3 * 3] &&\n          vertices[vtx2 * 3 + 1] === vertices[vtx3 * 3 + 1] &&\n          vertices[vtx2 * 3 + 2] === vertices[vtx3 * 3 + 2])\n      )\n\n    for (i = 0; i < 3; i++) {\n      mgm[i] = new Matrix4()\n    }\n\n    const minPatches = body ? 0 : 20\n    const maxPatches = bottom ? 32 : 28\n\n    vertPerRow = segments + 1\n\n    let surfCount = 0\n\n    let vertCount = 0\n    let normCount = 0\n    let uvCount = 0\n\n    let indexCount = 0\n\n    for (let surf = minPatches; surf < maxPatches; surf++) {\n      // lid is in the middle of the data, patches 20-27,\n      // so ignore it for this part of the loop if the lid is not desired\n      if (lid || surf < 20 || surf >= 28) {\n        // get M * G * M matrix for x,y,z\n        for (i = 0; i < 3; i++) {\n          // get control patches\n          for (r = 0; r < 4; r++) {\n            for (c = 0; c < 4; c++) {\n              // transposed\n              g[c * 4 + r] = teapotVertices[teapotPatches[surf * 16 + r * 4 + c] * 3 + i]\n\n              // is the lid to be made larger, and is this a point on the lid\n              // that is X or Y?\n              if (fitLid && surf >= 20 && surf < 28 && i !== 2) {\n                // increase XY size by 7.7%, found empirically. I don't\n                // increase Z so that the teapot will continue to fit in the\n                // space -1 to 1 for Y (Y is up for the final model).\n                g[c * 4 + r] *= 1.077\n              }\n\n              // Blinn \"fixed\" the teapot by dividing Z by blinnScale, and that's the\n              // data we now use. The original teapot is taller. Fix it:\n              if (!blinn && i === 2) {\n                g[c * 4 + r] *= blinnScale\n              }\n            }\n          }\n\n          gmx.set(g[0], g[1], g[2], g[3], g[4], g[5], g[6], g[7], g[8], g[9], g[10], g[11], g[12], g[13], g[14], g[15])\n\n          tmtx.multiplyMatrices(gmx, ms)\n          mgm[i].multiplyMatrices(mst, tmtx)\n        }\n\n        // step along, get points, and output\n        for (sstep = 0; sstep <= segments; sstep++) {\n          s = sstep / segments\n\n          for (tstep = 0; tstep <= segments; tstep++) {\n            t = tstep / segments\n\n            // point from basis\n            // get power vectors and their derivatives\n            for (p = 4, sval = tval = 1.0; p--; ) {\n              sp[p] = sval\n              tp[p] = tval\n              sval *= s\n              tval *= t\n\n              if (p === 3) {\n                dsp[p] = dtp[p] = 0.0\n                dsval = dtval = 1.0\n              } else {\n                dsp[p] = dsval * (3 - p)\n                dtp[p] = dtval * (3 - p)\n                dsval *= s\n                dtval *= t\n              }\n            }\n\n            vsp.fromArray(sp)\n            vtp.fromArray(tp)\n            vdsp.fromArray(dsp)\n            vdtp.fromArray(dtp)\n\n            // do for x,y,z\n            for (i = 0; i < 3; i++) {\n              // multiply power vectors times matrix to get value\n              tcoord = vsp.clone()\n              tcoord.applyMatrix4(mgm[i])\n              vert[i] = tcoord.dot(vtp)\n\n              // get s and t tangent vectors\n              tcoord = vdsp.clone()\n              tcoord.applyMatrix4(mgm[i])\n              sdir[i] = tcoord.dot(vtp)\n\n              tcoord = vsp.clone()\n              tcoord.applyMatrix4(mgm[i])\n              tdir[i] = tcoord.dot(vdtp)\n            }\n\n            // find normal\n            vsdir.fromArray(sdir)\n            vtdir.fromArray(tdir)\n            norm.crossVectors(vtdir, vsdir)\n            norm.normalize()\n\n            // if X and Z length is 0, at the cusp, so point the normal up or down, depending on patch number\n            if (vert[0] === 0 && vert[1] === 0) {\n              // if above the middle of the teapot, normal points up, else down\n              normOut.set(0, vert[2] > maxHeight2 ? 1 : -1, 0)\n            } else {\n              // standard output: rotate on X axis\n              normOut.set(norm.x, norm.z, -norm.y)\n            }\n\n            // store it all\n            vertices[vertCount++] = trueSize * vert[0]\n            vertices[vertCount++] = trueSize * (vert[2] - maxHeight2)\n            vertices[vertCount++] = -trueSize * vert[1]\n\n            normals[normCount++] = normOut.x\n            normals[normCount++] = normOut.y\n            normals[normCount++] = normOut.z\n\n            uvs[uvCount++] = 1 - t\n            uvs[uvCount++] = 1 - s\n          }\n        }\n\n        // save the faces\n        for (sstep = 0; sstep < segments; sstep++) {\n          for (tstep = 0; tstep < segments; tstep++) {\n            v1 = surfCount * vertPerRow * vertPerRow + sstep * vertPerRow + tstep\n            v2 = v1 + 1\n            v3 = v2 + vertPerRow\n            v4 = v1 + vertPerRow\n\n            // Normals and UVs cannot be shared. Without clone(), you can see the consequences\n            // of sharing if you call geometry.applyMatrix4( matrix ).\n            if (notDegenerate(v1, v2, v3)) {\n              indices[indexCount++] = v1\n              indices[indexCount++] = v2\n              indices[indexCount++] = v3\n            }\n\n            if (notDegenerate(v1, v3, v4)) {\n              indices[indexCount++] = v1\n              indices[indexCount++] = v3\n              indices[indexCount++] = v4\n            }\n          }\n        }\n\n        // increment only if a surface was used\n        surfCount++\n      }\n    }\n\n    this.setIndex(new BufferAttribute(indices, 1))\n    this.setAttribute('position', new BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new BufferAttribute(normals, 3))\n    this.setAttribute('uv', new BufferAttribute(uvs, 2))\n\n    this.computeBoundingSphere()\n  }\n}\n\nexport { TeapotGeometry }\n"], "names": ["BufferGeometry", "Matrix4", "Vector3", "Vector4", "BufferAttribute"], "mappings": ";;;AAmDA,MAAM,uBAAuBA,MAAAA,eAAe;AAAA,EAC1C,YAAY,MAAM,UAAU,QAAQ,KAAK,MAAM,QAAQ,OAAO;AAE5D,UAAM,gBAAgB;AAAA;AAAA,MAEpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAED,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAED,UAAO;AAEP,WAAO,QAAQ;AAGf,eAAW,aAAa,SAAY,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK,EAAE,IAAI;AAG9E,aAAS,WAAW,SAAY,OAAO;AACvC,UAAM,QAAQ,SAAY,OAAO;AACjC,WAAO,SAAS,SAAY,OAAO;AAGnC,aAAS,WAAW,SAAY,OAAO;AASvC,UAAM,aAAa;AACnB,YAAQ,UAAU,SAAY,OAAO;AAGrC,UAAM,YAAY,QAAQ,QAAQ,IAAI;AAEtC,UAAM,aAAa,YAAY;AAC/B,UAAM,WAAW,OAAO;AAIxB,QAAI,eAAe,UAAU,IAAI,WAAW,KAAK,WAAW;AAC5D,oBAAgB,OAAO,KAAK,WAAW,KAAK,WAAW;AACvD,oBAAgB,OAAO,KAAK,WAAW,WAAW;AAElD,UAAM,UAAU,IAAI,YAAY,eAAe,CAAC;AAEhD,QAAI,cAAc,SAAS,IAAI;AAC/B,mBAAe,MAAM,IAAI;AACzB,mBAAe,OAAO,KAAK;AAC3B,oBAAgB,WAAW,MAAM,WAAW;AAE5C,UAAM,WAAW,IAAI,aAAa,cAAc,CAAC;AACjD,UAAM,UAAU,IAAI,aAAa,cAAc,CAAC;AAChD,UAAM,MAAM,IAAI,aAAa,cAAc,CAAC;AAG5C,UAAM,KAAK,IAAIC,cAAS;AACxB,OAAG,IAAI,IAAM,GAAK,IAAM,GAAK,GAAK,IAAM,GAAK,GAAK,IAAM,GAAK,GAAK,GAAK,GAAK,GAAK,GAAK,CAAG;AAEzF,UAAM,IAAI,CAAE;AACZ,QAAI,GAAG,GAAG;AAEV,UAAM,KAAK,CAAE;AACb,UAAM,KAAK,CAAE;AACb,UAAM,MAAM,CAAE;AACd,UAAM,MAAM,CAAE;AAId,UAAM,MAAM,CAAE;AAEd,UAAM,OAAO,CAAE;AACf,UAAM,OAAO,CAAE;AACf,UAAM,OAAO,CAAE;AAEf,UAAM,OAAO,IAAIC,cAAS;AAE1B,QAAI;AAEJ,QAAI,OAAO;AACX,QAAI;AAEJ,QAAI,GAAG,GAAG,MAAM,MAAM;AACtB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAEZ,UAAM,UAAU,IAAIA,cAAS;AAC7B,QAAI,IAAI,IAAI,IAAI;AAEhB,UAAM,MAAM,IAAID,cAAS;AACzB,UAAM,OAAO,IAAIA,cAAS;AAE1B,UAAM,MAAM,IAAIE,cAAS;AACzB,UAAM,MAAM,IAAIA,cAAS;AACzB,UAAM,OAAO,IAAIA,cAAS;AAC1B,UAAM,OAAO,IAAIA,cAAS;AAE1B,UAAM,QAAQ,IAAID,cAAS;AAC3B,UAAM,QAAQ,IAAIA,cAAS;AAE3B,UAAM,MAAM,GAAG,MAAO;AACtB,QAAI,UAAW;AAIf,UAAM,gBAAgB,CACpB,MACA,MACA,SAEA,EACG,SAAS,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,KACvC,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC,KAChD,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC,KACjD,SAAS,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,KACvC,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC,KAChD,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC,KACjD,SAAS,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,KACvC,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC,KAChD,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,OAAO,IAAI,CAAC;AAGtD,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,UAAI,CAAC,IAAI,IAAID,cAAS;AAAA,IACvB;AAED,UAAM,aAAa,OAAO,IAAI;AAC9B,UAAM,aAAa,SAAS,KAAK;AAEjC,iBAAa,WAAW;AAExB,QAAI,YAAY;AAEhB,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,UAAU;AAEd,QAAI,aAAa;AAEjB,aAAS,OAAO,YAAY,OAAO,YAAY,QAAQ;AAGrD,UAAI,OAAO,OAAO,MAAM,QAAQ,IAAI;AAElC,aAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEtB,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEtB,gBAAE,IAAI,IAAI,CAAC,IAAI,eAAe,cAAc,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAI1E,kBAAI,UAAU,QAAQ,MAAM,OAAO,MAAM,MAAM,GAAG;AAIhD,kBAAE,IAAI,IAAI,CAAC,KAAK;AAAA,cACjB;AAID,kBAAI,CAAC,SAAS,MAAM,GAAG;AACrB,kBAAE,IAAI,IAAI,CAAC,KAAK;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAED,cAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAE5G,eAAK,iBAAiB,KAAK,EAAE;AAC7B,cAAI,CAAC,EAAE,iBAAiB,KAAK,IAAI;AAAA,QAClC;AAGD,aAAK,QAAQ,GAAG,SAAS,UAAU,SAAS;AAC1C,cAAI,QAAQ;AAEZ,eAAK,QAAQ,GAAG,SAAS,UAAU,SAAS;AAC1C,gBAAI,QAAQ;AAIZ,iBAAK,IAAI,GAAG,OAAO,OAAO,GAAK,OAAO;AACpC,iBAAG,CAAC,IAAI;AACR,iBAAG,CAAC,IAAI;AACR,sBAAQ;AACR,sBAAQ;AAER,kBAAI,MAAM,GAAG;AACX,oBAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AAClB,wBAAQ,QAAQ;AAAA,cAChC,OAAqB;AACL,oBAAI,CAAC,IAAI,SAAS,IAAI;AACtB,oBAAI,CAAC,IAAI,SAAS,IAAI;AACtB,yBAAS;AACT,yBAAS;AAAA,cACV;AAAA,YACF;AAED,gBAAI,UAAU,EAAE;AAChB,gBAAI,UAAU,EAAE;AAChB,iBAAK,UAAU,GAAG;AAClB,iBAAK,UAAU,GAAG;AAGlB,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEtB,uBAAS,IAAI,MAAO;AACpB,qBAAO,aAAa,IAAI,CAAC,CAAC;AAC1B,mBAAK,CAAC,IAAI,OAAO,IAAI,GAAG;AAGxB,uBAAS,KAAK,MAAO;AACrB,qBAAO,aAAa,IAAI,CAAC,CAAC;AAC1B,mBAAK,CAAC,IAAI,OAAO,IAAI,GAAG;AAExB,uBAAS,IAAI,MAAO;AACpB,qBAAO,aAAa,IAAI,CAAC,CAAC;AAC1B,mBAAK,CAAC,IAAI,OAAO,IAAI,IAAI;AAAA,YAC1B;AAGD,kBAAM,UAAU,IAAI;AACpB,kBAAM,UAAU,IAAI;AACpB,iBAAK,aAAa,OAAO,KAAK;AAC9B,iBAAK,UAAW;AAGhB,gBAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,GAAG;AAElC,sBAAQ,IAAI,GAAG,KAAK,CAAC,IAAI,aAAa,IAAI,IAAI,CAAC;AAAA,YAC7D,OAAmB;AAEL,sBAAQ,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;AAAA,YACpC;AAGD,qBAAS,WAAW,IAAI,WAAW,KAAK,CAAC;AACzC,qBAAS,WAAW,IAAI,YAAY,KAAK,CAAC,IAAI;AAC9C,qBAAS,WAAW,IAAI,CAAC,WAAW,KAAK,CAAC;AAE1C,oBAAQ,WAAW,IAAI,QAAQ;AAC/B,oBAAQ,WAAW,IAAI,QAAQ;AAC/B,oBAAQ,WAAW,IAAI,QAAQ;AAE/B,gBAAI,SAAS,IAAI,IAAI;AACrB,gBAAI,SAAS,IAAI,IAAI;AAAA,UACtB;AAAA,QACF;AAGD,aAAK,QAAQ,GAAG,QAAQ,UAAU,SAAS;AACzC,eAAK,QAAQ,GAAG,QAAQ,UAAU,SAAS;AACzC,iBAAK,YAAY,aAAa,aAAa,QAAQ,aAAa;AAChE,iBAAK,KAAK;AACV,iBAAK,KAAK;AACV,iBAAK,KAAK;AAIV,gBAAI,cAAc,IAAI,IAAI,EAAE,GAAG;AAC7B,sBAAQ,YAAY,IAAI;AACxB,sBAAQ,YAAY,IAAI;AACxB,sBAAQ,YAAY,IAAI;AAAA,YACzB;AAED,gBAAI,cAAc,IAAI,IAAI,EAAE,GAAG;AAC7B,sBAAQ,YAAY,IAAI;AACxB,sBAAQ,YAAY,IAAI;AACxB,sBAAQ,YAAY,IAAI;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAGD;AAAA,MACD;AAAA,IACF;AAED,SAAK,SAAS,IAAIG,MAAe,gBAAC,SAAS,CAAC,CAAC;AAC7C,SAAK,aAAa,YAAY,IAAIA,MAAAA,gBAAgB,UAAU,CAAC,CAAC;AAC9D,SAAK,aAAa,UAAU,IAAIA,MAAAA,gBAAgB,SAAS,CAAC,CAAC;AAC3D,SAAK,aAAa,MAAM,IAAIA,MAAAA,gBAAgB,KAAK,CAAC,CAAC;AAEnD,SAAK,sBAAuB;AAAA,EAC7B;AACH;;"}