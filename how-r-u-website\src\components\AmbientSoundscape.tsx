'use client';

import { useEffect, useState } from 'react';

export function AmbientSoundscape() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.3);

  useEffect(() => {
    // Create ambient sound context
    let audioContext: AudioContext | null = null;
    let oscillator: OscillatorNode | null = null;
    let gainNode: GainNode | null = null;

    const startAmbientSound = () => {
      try {
        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        oscillator = audioContext.createOscillator();
        gainNode = audioContext.createGain();

        // Create a very low frequency ambient tone
        oscillator.frequency.setValueAtTime(40, audioContext.currentTime);
        oscillator.type = 'sine';
        
        // Very quiet volume
        gainNode.gain.setValueAtTime(volume * 0.1, audioContext.currentTime);
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.start();
        setIsPlaying(true);
      } catch (error) {
        console.log('Audio context not available');
      }
    };

    const stopAmbientSound = () => {
      if (oscillator) {
        oscillator.stop();
        oscillator = null;
      }
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      setIsPlaying(false);
    };

    // Start ambient sound on first user interaction
    const handleFirstInteraction = () => {
      startAmbientSound();
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('keydown', handleFirstInteraction);

    return () => {
      stopAmbientSound();
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };
  }, [volume]);

  return (
    <div className="fixed top-4 left-4 z-50">
      <div className="text-whisper-600 text-xs font-mono">
        {isPlaying ? '♪ ambient' : '♪ silent'}
      </div>
    </div>
  );
}
