{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/DreamyLogo.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface DreamyLogoProps {\n  onLetterClick?: (letter: string, index: number) => void;\n}\n\nexport function DreamyLogo({ onLetterClick }: DreamyLogoProps) {\n  const [isHovered, setIsHovered] = useState(false);\n  const [clickSequence, setClickSequence] = useState<number[]>([]);\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);\n  const logoRef = useRef<SVGSVGElement>(null);\n\n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const secretSequence = [0, 4, 1, 3, 2]; // h-u-o-r-w\n\n  const handleLetterClick = (letter: string, index: number) => {\n    const newSequence = [...clickSequence, index];\n    setClickSequence(newSequence);\n\n    // Activate letter with dreamy glow\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n\n    // Reset after 3 seconds\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 3000);\n\n    // Check if sequence matches secret pattern\n    if (newSequence.length === secretSequence.length) {\n      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);\n      if (isCorrect) {\n        onLetterClick?.('secret', -1);\n      }\n      setTimeout(() => setClickSequence([]), 1000);\n    }\n\n    onLetterClick?.(letter, index);\n  };\n\n  const letterVariants = {\n    initial: { \n      opacity: 0.7, \n      scale: 1,\n      rotateX: 0,\n      rotateY: 0,\n      z: 0,\n    },\n    hover: { \n      opacity: 1, \n      scale: 1.1,\n      rotateX: 10,\n      rotateY: 5,\n      z: 20,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    },\n    click: { \n      opacity: [1, 0.3, 1], \n      scale: [1, 0.8, 1.2, 1],\n      rotateX: [0, 15, -10, 0],\n      rotateY: [0, -15, 10, 0],\n      transition: { duration: 0.8, ease: \"easeInOut\" }\n    },\n    active: {\n      opacity: 1,\n      scale: 1.05,\n      filter: 'brightness(1.5) blur(0.5px)',\n      textShadow: '0 0 20px rgba(255, 255, 255, 0.8)',\n    }\n  };\n\n  return (\n    <div className=\"relative perspective-1000\">\n      {/* Dreamy background glow */}\n      <motion.div\n        className=\"absolute inset-0 -m-20\"\n        animate={{\n          background: [\n            'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',\n            'radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%)',\n            'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',\n          ]\n        }}\n        transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n      />\n\n      {/* 3D Logo Container */}\n      <motion.div\n        className=\"relative transform-gpu\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n        animate={{\n          rotateX: isHovered ? 5 : 0,\n          rotateY: isHovered ? 5 : 0,\n          scale: isHovered ? 1.05 : 1,\n        }}\n        transition={{ duration: 0.6, ease: \"easeOut\" }}\n        style={{ \n          transformStyle: 'preserve-3d',\n          perspective: '1000px'\n        }}\n      >\n        <svg\n          ref={logoRef}\n          width=\"266\"\n          height=\"275\"\n          viewBox=\"0 0 266 275\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-80 h-auto cursor-pointer filter drop-shadow-2xl\"\n          style={{\n            filter: isHovered \n              ? 'brightness(1.3) drop-shadow(0 0 30px rgba(255, 255, 255, 0.5))' \n              : 'brightness(1) drop-shadow(0 0 15px rgba(255, 255, 255, 0.2))',\n            transition: 'filter 0.6s ease'\n          }}\n        >\n          <g id=\"howru\">\n            {/* Letter H */}\n            <motion.path\n              id=\"h\"\n              d=\"M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z\"\n              fill={activeLetters[0] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[0] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('h', 0)}\n              className=\"cursor-pointer animate-letter-dance\"\n              style={{ \n                animationDelay: '0s',\n                transformOrigin: 'center',\n                filter: activeLetters[0] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter O */}\n            <motion.path\n              id=\"o\"\n              d=\"M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z\"\n              fill={activeLetters[1] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[1] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('o', 1)}\n              className=\"cursor-pointer animate-dreamy-float\"\n              style={{ \n                animationDelay: '1s',\n                transformOrigin: 'center',\n                filter: activeLetters[1] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter W */}\n            <motion.path\n              id=\"w\"\n              d=\"M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z\"\n              fill={activeLetters[2] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[2] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('w', 2)}\n              className=\"cursor-pointer animate-gentle-sway\"\n              style={{ \n                animationDelay: '2s',\n                transformOrigin: 'center',\n                filter: activeLetters[2] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter R */}\n            <motion.path\n              id=\"r\"\n              d=\"M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z\"\n              fill={activeLetters[3] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[3] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('r', 3)}\n              className=\"cursor-pointer animate-depth-shift\"\n              style={{ \n                animationDelay: '3s',\n                transformOrigin: 'center',\n                filter: activeLetters[3] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter U */}\n            <motion.path\n              id=\"u\"\n              d=\"M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z\"\n              fill={activeLetters[4] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[4] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('u', 4)}\n              className=\"cursor-pointer animate-whisper-glow\"\n              style={{ \n                animationDelay: '4s',\n                transformOrigin: 'center',\n                filter: activeLetters[4] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n          </g>\n        </svg>\n\n        {/* 3D depth layers */}\n        <motion.div\n          className=\"absolute inset-0 -z-10\"\n          animate={{\n            rotateX: isHovered ? 3 : 0,\n            rotateY: isHovered ? 3 : 0,\n            scale: isHovered ? 1.02 : 1,\n            opacity: isHovered ? 0.3 : 0.1,\n          }}\n          transition={{ duration: 0.6, ease: \"easeOut\" }}\n          style={{ \n            transformStyle: 'preserve-3d',\n            transform: 'translateZ(-20px)',\n            filter: 'blur(2px)',\n            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)'\n          }}\n        />\n      </motion.div>\n\n      {/* Floating particles around logo */}\n      <motion.div\n        className=\"absolute inset-0 pointer-events-none\"\n        animate={{\n          rotate: 360,\n        }}\n        transition={{ duration: 60, repeat: Infinity, ease: \"linear\" }}\n      >\n        {[...Array(8)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white rounded-full opacity-30\"\n            style={{\n              left: `${20 + Math.cos(i * 45 * Math.PI / 180) * 150}px`,\n              top: `${20 + Math.sin(i * 45 * Math.PI / 180) * 150}px`,\n            }}\n            animate={{\n              opacity: [0.1, 0.5, 0.1],\n              scale: [0.5, 1.5, 0.5],\n            }}\n            transition={{\n              duration: 3 + i * 0.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: i * 0.3,\n            }}\n          />\n        ))}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,WAAW,EAAE,aAAa,EAAmB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACjG,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAEtC,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,iBAAiB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,EAAE,YAAY;IAEpD,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,cAAc;eAAI;YAAe;SAAM;QAC7C,iBAAiB;QAEjB,mCAAmC;QACnC,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QAEjB,wBAAwB;QACxB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;QAEH,2CAA2C;QAC3C,IAAI,YAAY,MAAM,KAAK,eAAe,MAAM,EAAE;YAChD,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,cAAc,CAAC,EAAE;YACzE,IAAI,WAAW;gBACb,gBAAgB,UAAU,CAAC;YAC7B;YACA,WAAW,IAAM,iBAAiB,EAAE,GAAG;QACzC;QAEA,gBAAgB,QAAQ;IAC1B;IAEA,MAAM,iBAAiB;QACrB,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;YACT,GAAG;QACL;QACA,OAAO;YACL,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,OAAO;YACL,SAAS;gBAAC;gBAAG;gBAAK;aAAE;YACpB,OAAO;gBAAC;gBAAG;gBAAK;gBAAK;aAAE;YACvB,SAAS;gBAAC;gBAAG;gBAAI,CAAC;gBAAI;aAAE;YACxB,SAAS;gBAAC;gBAAG,CAAC;gBAAI;gBAAI;aAAE;YACxB,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAY;QACjD;QACA,QAAQ;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,YAAY;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,YAAY;wBACV;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAIjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;gBACjC,SAAS;oBACP,SAAS,YAAY,IAAI;oBACzB,SAAS,YAAY,IAAI;oBACzB,OAAO,YAAY,OAAO;gBAC5B;gBACA,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;gBAC7C,OAAO;oBACL,gBAAgB;oBAChB,aAAa;gBACf;;kCAEA,8OAAC;wBACC,KAAK;wBACL,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,WAAU;wBACV,OAAO;4BACL,QAAQ,YACJ,mEACA;4BACJ,YAAY;wBACd;kCAEA,cAAA,8OAAC;4BAAE,IAAG;;8CAEJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;;;;;;;;;;;;kCAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS,YAAY,IAAI;4BACzB,SAAS,YAAY,IAAI;4BACzB,OAAO,YAAY,OAAO;4BAC1B,SAAS,YAAY,MAAM;wBAC7B;wBACA,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,OAAO;4BACL,gBAAgB;4BAChB,WAAW;4BACX,QAAQ;4BACR,YAAY;wBACd;;;;;;;;;;;;0BAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,QAAQ;gBACV;gBACA,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;0BAE5D;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;4BACxD,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;wBACzD;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO;gCAAC;gCAAK;gCAAK;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU,IAAI,IAAI;4BAClB,QAAQ;4BACR,MAAM;4BACN,OAAO,IAAI;wBACb;uBAfK;;;;;;;;;;;;;;;;AAqBjB", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/AnimatedSVGLogo.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, useEffect } from 'react';\nimport { motion, useAnimation, useMotionValue, useTransform } from 'framer-motion';\nimport { gsap } from 'gsap';\n\ninterface AnimatedSVGLogoProps {\n  onLetterClick?: (letter: string, index: number) => void;\n}\n\nexport function AnimatedSVGLogo({ onLetterClick }: AnimatedSVGLogoProps) {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);\n  const [isHovered, setIsHovered] = useState(false);\n  const [clickSequence, setClickSequence] = useState<number[]>([]);\n  \n  const mouseX = useMotionValue(0);\n  const mouseY = useMotionValue(0);\n  \n  const rotateX = useTransform(mouseY, [-300, 300], [15, -15]);\n  const rotateY = useTransform(mouseX, [-300, 300], [-15, 15]);\n  \n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const secretSequence = [0, 4, 1, 3, 2];\n\n  useEffect(() => {\n    // Animation d'entrée épique avec GSAP\n    if (svgRef.current) {\n      const paths = svgRef.current.querySelectorAll('path');\n      \n      gsap.set(paths, { \n        scale: 0,\n        rotation: 360,\n        transformOrigin: 'center',\n        opacity: 0\n      });\n      \n      gsap.to(paths, {\n        scale: 1,\n        rotation: 0,\n        opacity: 1,\n        duration: 2,\n        ease: \"elastic.out(1, 0.5)\",\n        stagger: 0.3,\n        delay: 0.5\n      });\n      \n      // Animation continue de respiration\n      gsap.to(svgRef.current, {\n        scale: 1.05,\n        duration: 3,\n        ease: \"power2.inOut\",\n        yoyo: true,\n        repeat: -1\n      });\n    }\n  }, []);\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    \n    mouseX.set(e.clientX - centerX);\n    mouseY.set(e.clientY - centerY);\n  };\n\n  const handleLetterClick = (letter: string, index: number) => {\n    const newSequence = [...clickSequence, index];\n    setClickSequence(newSequence);\n\n    // Animation de clic avec GSAP\n    if (svgRef.current) {\n      const path = svgRef.current.querySelector(`#${letter}`);\n      if (path) {\n        gsap.to(path, {\n          scale: 1.5,\n          rotation: 360,\n          duration: 0.8,\n          ease: \"back.out(1.7)\",\n          yoyo: true,\n          repeat: 1,\n          transformOrigin: 'center'\n        });\n        \n        // Effet de particules\n        gsap.to(path, {\n          filter: 'drop-shadow(0 0 20px #ffffff) drop-shadow(0 0 40px #ffffff)',\n          duration: 0.5,\n          yoyo: true,\n          repeat: 3\n        });\n      }\n    }\n\n    // Activer la lettre\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 3000);\n\n    // Vérifier séquence secrète\n    if (newSequence.length === secretSequence.length) {\n      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);\n      if (isCorrect) {\n        // Animation secrète épique !\n        if (svgRef.current) {\n          const paths = svgRef.current.querySelectorAll('path');\n          gsap.to(paths, {\n            scale: 2,\n            rotation: 720,\n            duration: 3,\n            ease: \"power4.out\",\n            stagger: 0.1,\n            yoyo: true,\n            repeat: 1,\n            transformOrigin: 'center'\n          });\n          \n          gsap.to(svgRef.current, {\n            filter: 'hue-rotate(360deg) saturate(2) brightness(1.5)',\n            duration: 3,\n            ease: \"power2.inOut\"\n          });\n        }\n        onLetterClick?.('secret', -1);\n      }\n      setTimeout(() => setClickSequence([]), 1000);\n    }\n\n    onLetterClick?.(letter, index);\n  };\n\n  const handleHover = () => {\n    setIsHovered(true);\n    if (svgRef.current) {\n      gsap.to(svgRef.current.querySelectorAll('path'), {\n        scale: 1.1,\n        duration: 0.3,\n        ease: \"power2.out\",\n        stagger: 0.05,\n        transformOrigin: 'center'\n      });\n    }\n  };\n\n  const handleLeave = () => {\n    setIsHovered(false);\n    if (svgRef.current) {\n      gsap.to(svgRef.current.querySelectorAll('path'), {\n        scale: 1,\n        duration: 0.3,\n        ease: \"power2.out\",\n        stagger: 0.05,\n        transformOrigin: 'center'\n      });\n    }\n  };\n\n  return (\n    <div className=\"relative w-full h-[400px] flex items-center justify-center perspective-1000\">\n      {/* Effet de fond animé */}\n      <motion.div\n        className=\"absolute inset-0 opacity-20\"\n        animate={{\n          background: [\n            'radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%)',\n            'radial-gradient(circle at 80% 70%, rgba(255,255,255,0.2) 0%, transparent 50%)',\n            'radial-gradient(circle at 50% 50%, rgba(255,255,255,0.15) 0%, transparent 50%)',\n            'radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%)',\n          ]\n        }}\n        transition={{ duration: 8, repeat: Infinity, ease: \"easeInOut\" }}\n      />\n      \n      <motion.div\n        className=\"relative\"\n        style={{\n          rotateX,\n          rotateY,\n          transformStyle: 'preserve-3d'\n        }}\n        onMouseMove={handleMouseMove}\n        onMouseEnter={handleHover}\n        onMouseLeave={handleLeave}\n        whileHover={{ scale: 1.05 }}\n        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n      >\n        <motion.svg\n          ref={svgRef}\n          width=\"320\"\n          height=\"330\"\n          viewBox=\"0 0 266 275\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"cursor-pointer filter drop-shadow-2xl\"\n          initial={{ opacity: 0, scale: 0.5 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 1.5, ease: \"easeOut\" }}\n        >\n          {/* Définition des filtres pour effets spéciaux */}\n          <defs>\n            <filter id=\"glow\">\n              <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n              <feMerge> \n                <feMergeNode in=\"coloredBlur\"/>\n                <feMergeNode in=\"SourceGraphic\"/>\n              </feMerge>\n            </filter>\n            <filter id=\"turbulence\">\n              <feTurbulence baseFrequency=\"0.02\" numOctaves=\"3\" result=\"noise\"/>\n              <feDisplacementMap in=\"SourceGraphic\" in2=\"noise\" scale=\"2\"/>\n            </filter>\n          </defs>\n          \n          <g id=\"howru\">\n            {/* Letter H */}\n            <motion.path\n              id=\"h\"\n              d=\"M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z\"\n              fill={activeLetters[0] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('h', 0)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[0] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter O */}\n            <motion.path\n              id=\"o\"\n              d=\"M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z\"\n              fill={activeLetters[1] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('o', 1)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[1] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter W */}\n            <motion.path\n              id=\"w\"\n              d=\"M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z\"\n              fill={activeLetters[2] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('w', 2)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[2] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter R */}\n            <motion.path\n              id=\"r\"\n              d=\"M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z\"\n              fill={activeLetters[3] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('r', 3)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[3] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter U */}\n            <motion.path\n              id=\"u\"\n              d=\"M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z\"\n              fill={activeLetters[4] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('u', 4)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[4] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n          </g>\n        </motion.svg>\n      </motion.div>\n      \n      {/* Particules flottantes autour du logo */}\n      {[...Array(12)].map((_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n          style={{\n            left: `${50 + Math.cos(i * 30 * Math.PI / 180) * 200}px`,\n            top: `${50 + Math.sin(i * 30 * Math.PI / 180) * 200}px`,\n          }}\n          animate={{\n            scale: [0.5, 1.5, 0.5],\n            opacity: [0.1, 0.6, 0.1],\n            rotate: 360,\n          }}\n          transition={{\n            duration: 4 + i * 0.5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: i * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAUO,SAAS,gBAAgB,EAAE,aAAa,EAAwB;IACrE,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACjG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAE9B,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC;QAAI,CAAC;KAAG;IAC3D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG;IAE3D,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,iBAAiB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,QAAQ,OAAO,OAAO,CAAC,gBAAgB,CAAC;YAE9C,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;gBACd,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,SAAS;YACX;YAEA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;gBACb,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YAEA,oCAAoC;YACpC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;gBACtB,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;YACX;QACF;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG;QACvB,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG;IACzB;IAEA,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,cAAc;eAAI;YAAe;SAAM;QAC7C,iBAAiB;QAEjB,8BAA8B;QAC9B,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,OAAO,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ;YACtD,IAAI,MAAM;gBACR,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACZ,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,MAAM;oBACN,MAAM;oBACN,QAAQ;oBACR,iBAAiB;gBACnB;gBAEA,sBAAsB;gBACtB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACZ,QAAQ;oBACR,UAAU;oBACV,MAAM;oBACN,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QAEjB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;QAEH,4BAA4B;QAC5B,IAAI,YAAY,MAAM,KAAK,eAAe,MAAM,EAAE;YAChD,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,cAAc,CAAC,EAAE;YACzE,IAAI,WAAW;gBACb,6BAA6B;gBAC7B,IAAI,OAAO,OAAO,EAAE;oBAClB,MAAM,QAAQ,OAAO,OAAO,CAAC,gBAAgB,CAAC;oBAC9C,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;wBACb,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,QAAQ;wBACR,iBAAiB;oBACnB;oBAEA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;wBACtB,QAAQ;wBACR,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA,gBAAgB,UAAU,CAAC;YAC7B;YACA,WAAW,IAAM,iBAAiB,EAAE,GAAG;QACzC;QAEA,gBAAgB,QAAQ;IAC1B;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,IAAI,OAAO,OAAO,EAAE;YAClB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,CAAC,gBAAgB,CAAC,SAAS;gBAC/C,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,IAAI,OAAO,OAAO,EAAE;YAClB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,CAAC,gBAAgB,CAAC,SAAS;gBAC/C,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,YAAY;wBACV;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL;oBACA;oBACA,gBAAgB;gBAClB;gBACA,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;0BAE1D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAG7C,8OAAC;;8CACC,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAe,cAAa;4CAAI,QAAO;;;;;;sDACxC,8OAAC;;8DACC,8OAAC;oDAAY,IAAG;;;;;;8DAChB,8OAAC;oDAAY,IAAG;;;;;;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAa,eAAc;4CAAO,YAAW;4CAAI,QAAO;;;;;;sDACzD,8OAAC;4CAAkB,IAAG;4CAAgB,KAAI;4CAAQ,OAAM;;;;;;;;;;;;;;;;;;sCAI5D,8OAAC;4BAAE,IAAG;;8CAEJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;;;;;;;;;;;;;;;;;;YAO/B;mBAAI,MAAM;aAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;wBACxD,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;oBACzD;oBACA,SAAS;wBACP,OAAO;4BAAC;4BAAK;4BAAK;yBAAI;wBACtB,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;wBACxB,QAAQ;oBACV;oBACA,YAAY;wBACV,UAAU,IAAI,IAAI;wBAClB,QAAQ;wBACR,MAAM;wBACN,OAAO,IAAI;oBACb;mBAhBK;;;;;;;;;;;AAqBf", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/Logo3D.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, useFrame, useThree } from '@react-three/fiber';\nimport { Text3D, Center, Float, MeshDistortMaterial, Sphere, Environment, Effects } from '@react-three/drei';\nimport { motion } from 'framer-motion';\nimport * as THREE from 'three';\n\ninterface Letter3DProps {\n  letter: string;\n  position: [number, number, number];\n  index: number;\n  isActive: boolean;\n  onClick: () => void;\n}\n\nfunction Letter3D({ letter, position, index, isActive, onClick }: Letter3DProps) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  \n  useFrame((state) => {\n    if (meshRef.current) {\n      // Animation de base flottante\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.3;\n      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;\n      meshRef.current.rotation.z = Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;\n      \n      // Animation quand actif\n      if (isActive) {\n        meshRef.current.rotation.y += 0.02;\n        meshRef.current.scale.setScalar(1.2 + Math.sin(state.clock.elapsedTime * 3) * 0.1);\n      } else {\n        meshRef.current.scale.setScalar(hovered ? 1.1 : 1);\n      }\n    }\n  });\n\n  return (\n    <Float\n      speed={2}\n      rotationIntensity={0.5}\n      floatIntensity={0.5}\n    >\n      <Text3D\n        ref={meshRef}\n        font=\"/fonts/helvetiker_regular.typeface.json\"\n        size={1.5}\n        height={0.3}\n        position={position}\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n        onClick={onClick}\n        curveSegments={12}\n      >\n        {letter}\n        <MeshDistortMaterial\n          color={isActive ? \"#ffffff\" : hovered ? \"#e0e0e0\" : \"#a0a0a0\"}\n          distort={isActive ? 0.4 : hovered ? 0.2 : 0.1}\n          speed={isActive ? 3 : 1}\n          roughness={0.1}\n          metalness={0.8}\n        />\n      </Text3D>\n    </Float>\n  );\n}\n\nfunction ParticleField() {\n  const pointsRef = useRef<THREE.Points>(null);\n  const particleCount = 200;\n  \n  const positions = new Float32Array(particleCount * 3);\n  for (let i = 0; i < particleCount; i++) {\n    positions[i * 3] = (Math.random() - 0.5) * 20;\n    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n  }\n\n  useFrame((state) => {\n    if (pointsRef.current) {\n      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;\n      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;\n    }\n  });\n\n  return (\n    <points ref={pointsRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.02}\n        color=\"#ffffff\"\n        transparent\n        opacity={0.6}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\nfunction BackgroundSphere() {\n  const sphereRef = useRef<THREE.Mesh>(null);\n  \n  useFrame((state) => {\n    if (sphereRef.current) {\n      sphereRef.current.rotation.y = state.clock.elapsedTime * 0.01;\n      sphereRef.current.rotation.x = state.clock.elapsedTime * 0.005;\n    }\n  });\n\n  return (\n    <Sphere ref={sphereRef} args={[15, 32, 32]} position={[0, 0, -10]}>\n      <MeshDistortMaterial\n        color=\"#111111\"\n        distort={0.3}\n        speed={1}\n        roughness={0.8}\n        metalness={0.2}\n        transparent\n        opacity={0.3}\n      />\n    </Sphere>\n  );\n}\n\nfunction CameraController() {\n  const { camera } = useThree();\n  \n  useFrame((state) => {\n    camera.position.x = Math.sin(state.clock.elapsedTime * 0.1) * 2;\n    camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 1;\n    camera.lookAt(0, 0, 0);\n  });\n  \n  return null;\n}\n\ninterface Logo3DProps {\n  onLetterClick?: (letter: string, index: number) => void;\n}\n\nexport function Logo3D({ onLetterClick }: Logo3DProps) {\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);\n  const [clickSequence, setClickSequence] = useState<number[]>([]);\n  \n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const positions: [number, number, number][] = [\n    [-3, 0, 0],\n    [-1.5, 0, 0],\n    [0, 0, 0],\n    [1.5, 0, 0],\n    [3, 0, 0]\n  ];\n  \n  const secretSequence = [0, 4, 1, 3, 2]; // h-u-o-r-w\n\n  const handleLetterClick = (letter: string, index: number) => {\n    const newSequence = [...clickSequence, index];\n    setClickSequence(newSequence);\n\n    // Activer la lettre avec animation\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n\n    // Reset après 3 secondes\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 3000);\n\n    // Vérifier la séquence secrète\n    if (newSequence.length === secretSequence.length) {\n      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);\n      if (isCorrect) {\n        onLetterClick?.('secret', -1);\n        // Animation spéciale pour toutes les lettres\n        setActiveLetters([true, true, true, true, true]);\n        setTimeout(() => setActiveLetters([false, false, false, false, false]), 5000);\n      }\n      setTimeout(() => setClickSequence([]), 1000);\n    }\n\n    onLetterClick?.(letter, index);\n  };\n\n  return (\n    <div className=\"w-full h-[600px] relative\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 2, ease: \"easeOut\" }}\n        className=\"w-full h-full\"\n      >\n        <Canvas\n          camera={{ position: [0, 0, 8], fov: 50 }}\n          gl={{ antialias: true, alpha: true }}\n          style={{ background: 'transparent' }}\n        >\n          <ambientLight intensity={0.3} />\n          <pointLight position={[10, 10, 10]} intensity={1} />\n          <pointLight position={[-10, -10, -10]} intensity={0.5} color=\"#4a5568\" />\n          \n          <Environment preset=\"night\" />\n          \n          <BackgroundSphere />\n          <ParticleField />\n          \n          <Center>\n            {letters.map((letter, index) => (\n              <Letter3D\n                key={letter + index}\n                letter={letter}\n                position={positions[index]}\n                index={index}\n                isActive={activeLetters[index]}\n                onClick={() => handleLetterClick(letter, index)}\n              />\n            ))}\n          </Center>\n          \n          <CameraController />\n        </Canvas>\n      </motion.div>\n      \n      {/* Instructions flottantes */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 2, duration: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center\"\n      >\n        <p className=\"text-whisper-400 text-sm font-light mb-2\">\n          cliquez sur les lettres pour les animer\n        </p>\n        <p className=\"text-whisper-600 text-xs font-light\">\n          trouvez la séquence secrète...\n        </p>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAgBA,SAAS,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAiB;IAC7E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,8BAA8B;YAC9B,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,SAAS;YACvF,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,SAAS;YAC/E,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,SAAS;YAE/E,wBAAwB;YACxB,IAAI,UAAU;gBACZ,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YAChF,OAAO;gBACL,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,MAAM;YAClD;QACF;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QACJ,OAAO;QACP,mBAAmB;QACnB,gBAAgB;kBAEhB,cAAA,8OAAC,0JAAA,CAAA,SAAM;YACL,KAAK;YACL,MAAK;YACL,MAAM;YACN,QAAQ;YACR,UAAU;YACV,eAAe,IAAM,WAAW;YAChC,cAAc,IAAM,WAAW;YAC/B,SAAS;YACT,eAAe;;gBAEd;8BACD,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,OAAO,WAAW,YAAY,UAAU,YAAY;oBACpD,SAAS,WAAW,MAAM,UAAU,MAAM;oBAC1C,OAAO,WAAW,IAAI;oBACtB,WAAW;oBACX,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IACvC,MAAM,gBAAgB;IAEtB,MAAM,YAAY,IAAI,aAAa,gBAAgB;IACnD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;IACjD;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;0BACC,cAAA,8OAAC;oBACC,QAAO;oBACP,OAAO;oBACP,OAAO;oBACP,UAAU;;;;;;;;;;;0BAGd,8OAAC;gBACC,MAAM;gBACN,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAErC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC,0JAAA,CAAA,SAAM;QAAC,KAAK;QAAW,MAAM;YAAC;YAAI;YAAI;SAAG;QAAE,UAAU;YAAC;YAAG;YAAG,CAAC;SAAG;kBAC/D,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;YAClB,OAAM;YACN,SAAS;YACT,OAAO;YACP,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;;;;;;;;;;;AAIjB;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE1B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC9D,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;QAC/D,OAAO,MAAM,CAAC,GAAG,GAAG;IACtB;IAEA,OAAO;AACT;AAMO,SAAS,OAAO,EAAE,aAAa,EAAe;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACjG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,YAAwC;QAC5C;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC,CAAC;YAAK;YAAG;SAAE;QACZ;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAK;YAAG;SAAE;QACX;YAAC;YAAG;YAAG;SAAE;KACV;IAED,MAAM,iBAAiB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,EAAE,YAAY;IAEpD,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,cAAc;eAAI;YAAe;SAAM;QAC7C,iBAAiB;QAEjB,mCAAmC;QACnC,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QAEjB,yBAAyB;QACzB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;QAEH,+BAA+B;QAC/B,IAAI,YAAY,MAAM,KAAK,eAAe,MAAM,EAAE;YAChD,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,cAAc,CAAC,EAAE;YACzE,IAAI,WAAW;gBACb,gBAAgB,UAAU,CAAC;gBAC3B,6CAA6C;gBAC7C,iBAAiB;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;gBAC/C,WAAW,IAAM,iBAAiB;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM,GAAG;YAC1E;YACA,WAAW,IAAM,iBAAiB,EAAE,GAAG;QACzC;QAEA,gBAAgB,QAAQ;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAU;gBAC3C,WAAU;0BAEV,cAAA,8OAAC,mMAAA,CAAA,SAAM;oBACL,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,IAAI;wBAAE,WAAW;wBAAM,OAAO;oBAAK;oBACnC,OAAO;wBAAE,YAAY;oBAAc;;sCAEnC,8OAAC;4BAAa,WAAW;;;;;;sCACzB,8OAAC;4BAAW,UAAU;gCAAC;gCAAI;gCAAI;6BAAG;4BAAE,WAAW;;;;;;sCAC/C,8OAAC;4BAAW,UAAU;gCAAC,CAAC;gCAAI,CAAC;gCAAI,CAAC;6BAAG;4BAAE,WAAW;4BAAK,OAAM;;;;;;sCAE7D,8OAAC,+JAAA,CAAA,cAAW;4BAAC,QAAO;;;;;;sCAEpB,8OAAC;;;;;sCACD,8OAAC;;;;;sCAED,8OAAC,0JAAA,CAAA,SAAM;sCACJ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;oCAEC,QAAQ;oCACR,UAAU,SAAS,CAAC,MAAM;oCAC1B,OAAO;oCACP,UAAU,aAAa,CAAC,MAAM;oCAC9B,SAAS,IAAM,kBAAkB,QAAQ;mCALpC,SAAS;;;;;;;;;;sCAUpB,8OAAC;;;;;;;;;;;;;;;;0BAKL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAG,UAAU;gBAAE;gBACpC,WAAU;;kCAEV,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;kCAGxD,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/Scene3D.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';\nimport { \n  Text3D, \n  Center, \n  Float, \n  MeshDistortMaterial, \n  Sphere, \n  Environment,\n  OrbitControls,\n  Sparkles,\n  Stars,\n  Cloud\n} from '@react-three/drei';\nimport { EffectComposer, Bloom, ChromaticAberration, Glitch } from '@react-three/postprocessing';\nimport { BlendFunction } from 'postprocessing';\nimport * as THREE from 'three';\n\nfunction FloatingText({ text, position, color = \"#ffffff\", isActive = false }: {\n  text: string;\n  position: [number, number, number];\n  color?: string;\n  isActive?: boolean;\n}) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  \n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.5;\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.3;\n      \n      if (isActive) {\n        meshRef.current.scale.setScalar(1.5 + Math.sin(state.clock.elapsedTime * 5) * 0.2);\n      }\n    }\n  });\n\n  return (\n    <Float speed={3} rotationIntensity={0.5} floatIntensity={2}>\n      <Text3D\n        ref={meshRef}\n        font=\"/fonts/helvetiker_regular.typeface.json\"\n        size={2}\n        height={0.5}\n        position={position}\n        curveSegments={12}\n      >\n        {text}\n        <MeshDistortMaterial\n          color={color}\n          distort={isActive ? 0.6 : 0.2}\n          speed={isActive ? 5 : 2}\n          roughness={0.1}\n          metalness={0.8}\n        />\n      </Text3D>\n    </Float>\n  );\n}\n\nfunction DreamSphere() {\n  const sphereRef = useRef<THREE.Mesh>(null);\n  \n  useFrame((state) => {\n    if (sphereRef.current) {\n      sphereRef.current.rotation.x = state.clock.elapsedTime * 0.1;\n      sphereRef.current.rotation.y = state.clock.elapsedTime * 0.15;\n      sphereRef.current.position.y = Math.sin(state.clock.elapsedTime) * 2;\n    }\n  });\n\n  return (\n    <Sphere ref={sphereRef} args={[3, 64, 64]} position={[0, 0, -8]}>\n      <MeshDistortMaterial\n        color=\"#4a5568\"\n        distort={0.4}\n        speed={2}\n        roughness={0.2}\n        metalness={0.9}\n        transparent\n        opacity={0.6}\n      />\n    </Sphere>\n  );\n}\n\nfunction ParticleSystem() {\n  const pointsRef = useRef<THREE.Points>(null);\n  const particleCount = 500;\n  \n  const positions = new Float32Array(particleCount * 3);\n  const colors = new Float32Array(particleCount * 3);\n  \n  for (let i = 0; i < particleCount; i++) {\n    positions[i * 3] = (Math.random() - 0.5) * 50;\n    positions[i * 3 + 1] = (Math.random() - 0.5) * 50;\n    positions[i * 3 + 2] = (Math.random() - 0.5) * 50;\n    \n    colors[i * 3] = Math.random();\n    colors[i * 3 + 1] = Math.random();\n    colors[i * 3 + 2] = Math.random();\n  }\n\n  useFrame((state) => {\n    if (pointsRef.current) {\n      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;\n      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;\n    }\n  });\n\n  return (\n    <points ref={pointsRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n        <bufferAttribute\n          attach=\"attributes-color\"\n          count={particleCount}\n          array={colors}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.05}\n        vertexColors\n        transparent\n        opacity={0.8}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\nfunction CameraAnimation() {\n  const { camera } = useThree();\n  \n  useFrame((state) => {\n    camera.position.x = Math.sin(state.clock.elapsedTime * 0.2) * 5;\n    camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 3;\n    camera.position.z = 15 + Math.sin(state.clock.elapsedTime * 0.1) * 5;\n    camera.lookAt(0, 0, 0);\n  });\n  \n  return null;\n}\n\ninterface Scene3DProps {\n  activeLetters?: boolean[];\n}\n\nexport function Scene3D({ activeLetters = [false, false, false, false, false] }: Scene3DProps) {\n  const [glitchActive, setGlitchActive] = useState(false);\n  \n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const positions: [number, number, number][] = [\n    [-8, 2, 0],\n    [-4, -1, 2],\n    [0, 3, -1],\n    [4, -2, 1],\n    [8, 1, -2]\n  ];\n\n  return (\n    <div className=\"w-full h-screen\">\n      <Canvas\n        camera={{ position: [0, 0, 15], fov: 75 }}\n        gl={{ antialias: true, alpha: true }}\n      >\n        {/* Éclairage dramatique */}\n        <ambientLight intensity={0.2} />\n        <pointLight position={[10, 10, 10]} intensity={1} color=\"#ffffff\" />\n        <pointLight position={[-10, -10, -10]} intensity={0.5} color=\"#4a5568\" />\n        <spotLight\n          position={[0, 20, 0]}\n          angle={0.3}\n          penumbra={1}\n          intensity={1}\n          castShadow\n        />\n        \n        {/* Environnement */}\n        <Environment preset=\"night\" />\n        <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade />\n        \n        {/* Nuages atmosphériques */}\n        <Cloud\n          position={[-20, 10, -20]}\n          speed={0.2}\n          opacity={0.1}\n          width={10}\n          depth={1.5}\n          segments={20}\n        />\n        <Cloud\n          position={[20, -10, -20]}\n          speed={0.3}\n          opacity={0.1}\n          width={15}\n          depth={2}\n          segments={25}\n        />\n        \n        {/* Système de particules */}\n        <ParticleSystem />\n        <Sparkles count={100} scale={[20, 20, 20]} size={2} speed={0.5} />\n        \n        {/* Sphère de rêve */}\n        <DreamSphere />\n        \n        {/* Lettres 3D flottantes */}\n        <Center>\n          {letters.map((letter, index) => (\n            <FloatingText\n              key={letter}\n              text={letter}\n              position={positions[index]}\n              color={activeLetters[index] ? \"#ffffff\" : \"#a0a0a0\"}\n              isActive={activeLetters[index]}\n            />\n          ))}\n        </Center>\n        \n        {/* Animation de caméra */}\n        <CameraAnimation />\n        \n        {/* Contrôles optionnels */}\n        <OrbitControls\n          enablePan={false}\n          enableZoom={false}\n          enableRotate={true}\n          autoRotate\n          autoRotateSpeed={0.5}\n        />\n        \n        {/* Effets de post-processing */}\n        <EffectComposer>\n          <Bloom\n            intensity={1.5}\n            luminanceThreshold={0.2}\n            luminanceSmoothing={0.9}\n            height={300}\n          />\n          <ChromaticAberration\n            blendFunction={BlendFunction.NORMAL}\n            offset={[0.002, 0.002]}\n          />\n          {glitchActive && (\n            <Glitch\n              delay={[1.5, 3.5]}\n              duration={[0.6, 1.0]}\n              strength={[0.3, 1.0]}\n              mode={0}\n            />\n          )}\n        </EffectComposer>\n      </Canvas>\n      \n      {/* Contrôles UI */}\n      <div className=\"absolute bottom-8 left-8 space-y-2\">\n        <button\n          onClick={() => setGlitchActive(!glitchActive)}\n          className=\"px-4 py-2 bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light hover:bg-ghost-200 transition-colors\"\n        >\n          {glitchActive ? 'Désactiver Glitch' : 'Activer Glitch'}\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAjBA;;;;;;;AAoBA,SAAS,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,SAAS,EAAE,WAAW,KAAK,EAK1E;IACC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YACnF,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;YAEjE,IAAI,UAAU;gBACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YAChF;QACF;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAG,mBAAmB;QAAK,gBAAgB;kBACvD,cAAA,8OAAC,0JAAA,CAAA,SAAM;YACL,KAAK;YACL,MAAK;YACL,MAAM;YACN,QAAQ;YACR,UAAU;YACV,eAAe;;gBAEd;8BACD,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,SAAS,WAAW,MAAM;oBAC1B,OAAO,WAAW,IAAI;oBACtB,WAAW;oBACX,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAErC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;QACrE;IACF;IAEA,qBACE,8OAAC,0JAAA,CAAA,SAAM;QAAC,KAAK;QAAW,MAAM;YAAC;YAAG;YAAI;SAAG;QAAE,UAAU;YAAC;YAAG;YAAG,CAAC;SAAE;kBAC7D,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;YAClB,OAAM;YACN,SAAS;YACT,OAAO;YACP,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;;;;;;;;;;;AAIjB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IACvC,MAAM,gBAAgB;IAEtB,MAAM,YAAY,IAAI,aAAa,gBAAgB;IACnD,MAAM,SAAS,IAAI,aAAa,gBAAgB;IAEhD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAE/C,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM;QAC3B,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM;QAC/B,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM;IACjC;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;;kCACC,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAEZ,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;;;;;;;0BAGd,8OAAC;gBACC,MAAM;gBACN,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE1B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC9D,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;QAC/D,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACnE,OAAO,MAAM,CAAC,GAAG,GAAG;IACtB;IAEA,OAAO;AACT;AAMO,SAAS,QAAQ,EAAE,gBAAgB;IAAC;IAAO;IAAO;IAAO;IAAO;CAAM,EAAgB;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,YAAwC;QAC5C;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC,CAAC;YAAG,CAAC;YAAG;SAAE;QACX;YAAC;YAAG;YAAG,CAAC;SAAE;QACV;YAAC;YAAG,CAAC;YAAG;SAAE;QACV;YAAC;YAAG;YAAG,CAAC;SAAE;KACX;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBACL,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAG;oBAAE,KAAK;gBAAG;gBACxC,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;;kCAGnC,8OAAC;wBAAa,WAAW;;;;;;kCACzB,8OAAC;wBAAW,UAAU;4BAAC;4BAAI;4BAAI;yBAAG;wBAAE,WAAW;wBAAG,OAAM;;;;;;kCACxD,8OAAC;wBAAW,UAAU;4BAAC,CAAC;4BAAI,CAAC;4BAAI,CAAC;yBAAG;wBAAE,WAAW;wBAAK,OAAM;;;;;;kCAC7D,8OAAC;wBACC,UAAU;4BAAC;4BAAG;4BAAI;yBAAE;wBACpB,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;;;;;;kCAIZ,8OAAC,+JAAA,CAAA,cAAW;wBAAC,QAAO;;;;;;kCACpB,8OAAC,yJAAA,CAAA,QAAK;wBAAC,QAAQ;wBAAK,OAAO;wBAAI,OAAO;wBAAM,QAAQ;wBAAG,YAAY;wBAAG,IAAI;;;;;;kCAG1E,8OAAC,yJAAA,CAAA,QAAK;wBACJ,UAAU;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;wBACxB,OAAO;wBACP,SAAS;wBACT,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAEZ,8OAAC,yJAAA,CAAA,QAAK;wBACJ,UAAU;4BAAC;4BAAI,CAAC;4BAAI,CAAC;yBAAG;wBACxB,OAAO;wBACP,SAAS;wBACT,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAIZ,8OAAC;;;;;kCACD,8OAAC,4JAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAK,OAAO;4BAAC;4BAAI;4BAAI;yBAAG;wBAAE,MAAM;wBAAG,OAAO;;;;;;kCAG3D,8OAAC;;;;;kCAGD,8OAAC,0JAAA,CAAA,SAAM;kCACJ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gCAEC,MAAM;gCACN,UAAU,SAAS,CAAC,MAAM;gCAC1B,OAAO,aAAa,CAAC,MAAM,GAAG,YAAY;gCAC1C,UAAU,aAAa,CAAC,MAAM;+BAJzB;;;;;;;;;;kCAUX,8OAAC;;;;;kCAGD,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,UAAU;wBACV,iBAAiB;;;;;;kCAInB,8OAAC,mKAAA,CAAA,iBAAc;;0CACb,8OAAC,mKAAA,CAAA,QAAK;gCACJ,WAAW;gCACX,oBAAoB;gCACpB,oBAAoB;gCACpB,QAAQ;;;;;;0CAEV,8OAAC,mKAAA,CAAA,sBAAmB;gCAClB,eAAe,gJAAA,CAAA,gBAAa,CAAC,MAAM;gCACnC,QAAQ;oCAAC;oCAAO;iCAAM;;;;;;4BAEvB,8BACC,8OAAC,mKAAA,CAAA,SAAM;gCACL,OAAO;oCAAC;oCAAK;iCAAI;gCACjB,UAAU;oCAAC;oCAAK;iCAAI;gCACpB,UAAU;oCAAC;oCAAK;iCAAI;gCACpB,MAAM;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS,IAAM,gBAAgB,CAAC;oBAChC,WAAU;8BAET,eAAe,sBAAsB;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/EasterEggHunter.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface EasterEggHunterProps {\n  onSecretFound: (secretId: string) => void;\n}\n\nexport function EasterEggHunter({ onSecretFound }: EasterEggHunterProps) {\n  const [konamiSequence, setKonamiSequence] = useState<string[]>([]);\n  const [clickCount, setClickCount] = useState(0);\n  const [lastClickTime, setLastClickTime] = useState(0);\n\n  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      const newSequence = [...konamiSequence, e.code];\n      \n      // Keep only the last 10 keys\n      if (newSequence.length > 10) {\n        newSequence.shift();\n      }\n      \n      setKonamiSequence(newSequence);\n      \n      // Check if konami code is complete\n      if (newSequence.length === 10 && \n          newSequence.every((key, index) => key === konamiCode[index])) {\n        onSecretFound('konami_code');\n        setKonamiSequence([]);\n      }\n    };\n\n    const handleClick = (e: MouseEvent) => {\n      const now = Date.now();\n      \n      // Reset if too much time has passed\n      if (now - lastClickTime > 1000) {\n        setClickCount(1);\n      } else {\n        setClickCount(prev => prev + 1);\n      }\n      \n      setLastClickTime(now);\n      \n      // Secret: 7 rapid clicks\n      if (clickCount >= 7) {\n        onSecretFound('rapid_clicks');\n        setClickCount(0);\n      }\n      \n      // Secret: clicking in corners\n      const { clientX, clientY } = e;\n      const { innerWidth, innerHeight } = window;\n      \n      if ((clientX < 50 && clientY < 50) || \n          (clientX > innerWidth - 50 && clientY < 50) ||\n          (clientX < 50 && clientY > innerHeight - 50) ||\n          (clientX > innerWidth - 50 && clientY > innerHeight - 50)) {\n        onSecretFound('corner_click');\n      }\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      // Secret: mouse idle in center for 5 seconds\n      const { clientX, clientY } = e;\n      const { innerWidth, innerHeight } = window;\n      \n      if (Math.abs(clientX - innerWidth / 2) < 20 && \n          Math.abs(clientY - innerHeight / 2) < 20) {\n        setTimeout(() => {\n          onSecretFound('center_idle');\n        }, 5000);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('click', handleClick);\n    document.addEventListener('mousemove', handleMouseMove);\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('click', handleClick);\n      document.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, [konamiSequence, clickCount, lastClickTime, onSecretFound]);\n\n  return null; // This component is invisible\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAQO,SAAS,gBAAgB,EAAE,aAAa,EAAwB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa;QAAC;QAAW;QAAW;QAAa;QAAa;QAAa;QAAc;QAAa;QAAc;QAAQ;KAAO;IAEzI,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,MAAM,cAAc;mBAAI;gBAAgB,EAAE,IAAI;aAAC;YAE/C,6BAA6B;YAC7B,IAAI,YAAY,MAAM,GAAG,IAAI;gBAC3B,YAAY,KAAK;YACnB;YAEA,kBAAkB;YAElB,mCAAmC;YACnC,IAAI,YAAY,MAAM,KAAK,MACvB,YAAY,KAAK,CAAC,CAAC,KAAK,QAAU,QAAQ,UAAU,CAAC,MAAM,GAAG;gBAChE,cAAc;gBACd,kBAAkB,EAAE;YACtB;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,MAAM,MAAM,KAAK,GAAG;YAEpB,oCAAoC;YACpC,IAAI,MAAM,gBAAgB,MAAM;gBAC9B,cAAc;YAChB,OAAO;gBACL,cAAc,CAAA,OAAQ,OAAO;YAC/B;YAEA,iBAAiB;YAEjB,yBAAyB;YACzB,IAAI,cAAc,GAAG;gBACnB,cAAc;gBACd,cAAc;YAChB;YAEA,8BAA8B;YAC9B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAC7B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YAEpC,IAAI,AAAC,UAAU,MAAM,UAAU,MAC1B,UAAU,aAAa,MAAM,UAAU,MACvC,UAAU,MAAM,UAAU,cAAc,MACxC,UAAU,aAAa,MAAM,UAAU,cAAc,IAAK;gBAC7D,cAAc;YAChB;QACF;QAEA,MAAM,kBAAkB,CAAC;YACvB,6CAA6C;YAC7C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAC7B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YAEpC,IAAI,KAAK,GAAG,CAAC,UAAU,aAAa,KAAK,MACrC,KAAK,GAAG,CAAC,UAAU,cAAc,KAAK,IAAI;gBAC5C,WAAW;oBACT,cAAc;gBAChB,GAAG;YACL;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAgB;QAAY;QAAe;KAAc;IAE7D,OAAO,MAAM,8BAA8B;AAC7C", "debugId": null}}, {"offset": {"line": 1905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/AmbientSoundscape.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport function AmbientSoundscape() {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [volume, setVolume] = useState(0.3);\n\n  useEffect(() => {\n    // Create ambient sound context\n    let audioContext: AudioContext | null = null;\n    let oscillator: OscillatorNode | null = null;\n    let gainNode: GainNode | null = null;\n\n    const startAmbientSound = () => {\n      try {\n        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n        oscillator = audioContext.createOscillator();\n        gainNode = audioContext.createGain();\n\n        // Create a very low frequency ambient tone\n        oscillator.frequency.setValueAtTime(40, audioContext.currentTime);\n        oscillator.type = 'sine';\n        \n        // Very quiet volume\n        gainNode.gain.setValueAtTime(volume * 0.1, audioContext.currentTime);\n        \n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        \n        oscillator.start();\n        setIsPlaying(true);\n      } catch (error) {\n        console.log('Audio context not available');\n      }\n    };\n\n    const stopAmbientSound = () => {\n      if (oscillator) {\n        oscillator.stop();\n        oscillator = null;\n      }\n      if (audioContext) {\n        audioContext.close();\n        audioContext = null;\n      }\n      setIsPlaying(false);\n    };\n\n    // Start ambient sound on first user interaction\n    const handleFirstInteraction = () => {\n      startAmbientSound();\n      document.removeEventListener('click', handleFirstInteraction);\n      document.removeEventListener('keydown', handleFirstInteraction);\n    };\n\n    document.addEventListener('click', handleFirstInteraction);\n    document.addEventListener('keydown', handleFirstInteraction);\n\n    return () => {\n      stopAmbientSound();\n      document.removeEventListener('click', handleFirstInteraction);\n      document.removeEventListener('keydown', handleFirstInteraction);\n    };\n  }, [volume]);\n\n  return (\n    <div className=\"fixed top-4 left-4 z-50\">\n      <div className=\"text-whisper-600 text-xs font-mono\">\n        {isPlaying ? '♪ ambient' : '♪ silent'}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,IAAI,eAAoC;QACxC,IAAI,aAAoC;QACxC,IAAI,WAA4B;QAEhC,MAAM,oBAAoB;YACxB,IAAI;gBACF,eAAe,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;gBAC7E,aAAa,aAAa,gBAAgB;gBAC1C,WAAW,aAAa,UAAU;gBAElC,2CAA2C;gBAC3C,WAAW,SAAS,CAAC,cAAc,CAAC,IAAI,aAAa,WAAW;gBAChE,WAAW,IAAI,GAAG;gBAElB,oBAAoB;gBACpB,SAAS,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,aAAa,WAAW;gBAEnE,WAAW,OAAO,CAAC;gBACnB,SAAS,OAAO,CAAC,aAAa,WAAW;gBAEzC,WAAW,KAAK;gBAChB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,MAAM,mBAAmB;YACvB,IAAI,YAAY;gBACd,WAAW,IAAI;gBACf,aAAa;YACf;YACA,IAAI,cAAc;gBAChB,aAAa,KAAK;gBAClB,eAAe;YACjB;YACA,aAAa;QACf;QAEA,gDAAgD;QAChD,MAAM,yBAAyB;YAC7B;YACA,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,WAAW;QAC1C;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,WAAW;QAErC,OAAO;YACL;YACA,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,YAAY,cAAc;;;;;;;;;;;AAInC", "debugId": null}}, {"offset": {"line": 1988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/HiddenNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface HiddenNavigationProps {\n  secretsFound: string[];\n}\n\nexport function HiddenNavigation({ secretsFound }: HiddenNavigationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n      \n      // Show navigation when mouse is in top-left corner\n      if (e.clientX < 100 && e.clientY < 100) {\n        setIsVisible(true);\n      } else if (e.clientX > 200 || e.clientY > 200) {\n        setIsVisible(false);\n      }\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const navigationItems = [\n    { id: 'echoes', label: '◦ echoes', unlocked: secretsFound.includes('logo_sequence') },\n    { id: 'fragments', label: '◦ fragments', unlocked: secretsFound.includes('konami_code') },\n    { id: 'void', label: '◦ the void', unlocked: secretsFound.includes('rapid_clicks') },\n    { id: 'contact', label: '◦ contact', unlocked: secretsFound.length >= 3 },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.nav\n          initial={{ opacity: 0, x: -50 }}\n          animate={{ opacity: 1, x: 0 }}\n          exit={{ opacity: 0, x: -50 }}\n          transition={{ duration: 0.3 }}\n          className=\"fixed top-8 left-8 z-40\"\n        >\n          <div className=\"bg-void-600 border border-whisper-100 p-6 backdrop-blur-sm\">\n            <h3 className=\"text-whisper-300 text-sm font-mono mb-4\">navigate</h3>\n            <ul className=\"space-y-2\">\n              {navigationItems.map((item) => (\n                <li key={item.id}>\n                  <button\n                    className={`text-xs font-mono transition-colors duration-300 ${\n                      item.unlocked \n                        ? 'text-whisper-400 hover:text-whisper-200 cursor-pointer' \n                        : 'text-whisper-700 cursor-not-allowed'\n                    }`}\n                    disabled={!item.unlocked}\n                  >\n                    {item.label}\n                  </button>\n                </li>\n              ))}\n            </ul>\n            \n            <div className=\"mt-6 pt-4 border-t border-whisper-100\">\n              <p className=\"text-whisper-600 text-xs font-mono\">\n                secrets: {secretsFound.length}/7\n              </p>\n            </div>\n          </div>\n        </motion.nav>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASO,SAAS,iBAAiB,EAAE,YAAY,EAAyB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAE9C,mDAAmD;YACnD,IAAI,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,KAAK;gBACtC,aAAa;YACf,OAAO,IAAI,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,KAAK;gBAC7C,aAAa;YACf;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAU,OAAO;YAAY,UAAU,aAAa,QAAQ,CAAC;QAAiB;QACpF;YAAE,IAAI;YAAa,OAAO;YAAe,UAAU,aAAa,QAAQ,CAAC;QAAe;QACxF;YAAE,IAAI;YAAQ,OAAO;YAAc,UAAU,aAAa,QAAQ,CAAC;QAAgB;QACnF;YAAE,IAAI;YAAW,OAAO;YAAa,UAAU,aAAa,MAAM,IAAI;QAAE;KACzE;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAG,WAAU;kCACX,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;0CACC,cAAA,8OAAC;oCACC,WAAW,CAAC,iDAAiD,EAC3D,KAAK,QAAQ,GACT,2DACA,uCACJ;oCACF,UAAU,CAAC,KAAK,QAAQ;8CAEvB,KAAK,KAAK;;;;;;+BATN,KAAK,EAAE;;;;;;;;;;kCAepB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAqC;gCACtC,aAAa,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { DreamyLogo } from \"@/components/DreamyLogo\";\nimport { AnimatedSVGLogo } from \"@/components/AnimatedSVGLogo\";\nimport { Logo3D } from \"@/components/Logo3D\";\nimport { Scene3D } from \"@/components/Scene3D\";\nimport { EasterEggHunter } from \"@/components/EasterEggHunter\";\nimport { AmbientSoundscape } from \"@/components/AmbientSoundscape\";\nimport { HiddenNavigation } from \"@/components/HiddenNavigation\";\n\nexport default function Home() {\n  const [currentPhase, setCurrentPhase] = useState<\n    \"entry\" | \"void\" | \"exploration\"\n  >(\"entry\");\n  const [secretsFound, setSecretsFound] = useState<string[]>([]);\n  const [showHiddenText, setShowHiddenText] = useState(false);\n  const [logoMode, setLogoMode] = useState<\"2d\" | \"svg\" | \"3d\" | \"scene\">(\n    \"svg\"\n  );\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([\n    false,\n    false,\n    false,\n    false,\n    false,\n  ]);\n\n  useEffect(() => {\n    // Auto-transition from entry to void after 3 seconds\n    const timer = setTimeout(() => {\n      if (currentPhase === \"entry\") {\n        setCurrentPhase(\"void\");\n      }\n    }, 3000);\n\n    return () => clearTimeout(timer);\n  }, [currentPhase]);\n\n  const handleLogoInteraction = (letter: string, index: number) => {\n    if (letter === \"secret\") {\n      setSecretsFound((prev) => [...prev, \"logo_sequence\"]);\n      setCurrentPhase(\"exploration\");\n      // Animation spéciale pour toutes les lettres\n      setActiveLetters([true, true, true, true, true]);\n      setTimeout(\n        () => setActiveLetters([false, false, false, false, false]),\n        5000\n      );\n    } else {\n      // Activer la lettre cliquée\n      const newActiveLetters = [...activeLetters];\n      newActiveLetters[index] = true;\n      setActiveLetters(newActiveLetters);\n      setTimeout(() => {\n        const resetLetters = [...activeLetters];\n        resetLetters[index] = false;\n        setActiveLetters(resetLetters);\n      }, 3000);\n    }\n  };\n\n  const handleSecretFound = (secretId: string) => {\n    setSecretsFound((prev) => [...prev, secretId]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-void-700 relative overflow-hidden\">\n      <AmbientSoundscape />\n      <EasterEggHunter onSecretFound={handleSecretFound} />\n      <AnimatePresence mode=\"wait\">\n        {currentPhase === \"entry\" && (\n          <motion.div\n            key=\"entry\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 2 }}\n            className=\"fixed inset-0 flex items-center justify-center z-10\"\n          >\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ delay: 0.5, duration: 1.5 }}\n              className=\"text-center\"\n            >\n              <motion.div\n                className=\"text-whisper-400 text-sm font-mono mb-8 animate-whisper-glow\"\n                animate={{ opacity: [0.5, 1, 0.5] }}\n                transition={{ duration: 3, repeat: Infinity }}\n              >\n                entering the void...\n              </motion.div>\n              <h1\n                className=\"text-whisper-300 text-6xl font-light mb-4 animate-dreamy-float\"\n                style={{\n                  textShadow: \"0 0 30px var(--whisper-bright)\",\n                }}\n              >\n                how r u\n              </h1>\n              <div className=\"w-32 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto animate-gentle-sway\" />\n              <motion.div\n                className=\"text-whisper-500 text-xs font-light mt-4\"\n                animate={{ opacity: [0.3, 0.8, 0.3] }}\n                transition={{ duration: 4, repeat: Infinity }}\n              >\n                consciousness loading...\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        )}\n\n        {currentPhase === \"void\" && (\n          <motion.div\n            key=\"void\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 2 }}\n            className=\"fixed inset-0 flex flex-col items-center justify-center z-10\"\n          >\n            <motion.div\n              initial={{ y: 50, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ delay: 1, duration: 2 }}\n              className=\"text-center mb-16\"\n            >\n              {logoMode === \"2d\" && (\n                <DreamyLogo onLetterClick={handleLogoInteraction} />\n              )}\n              {logoMode === \"svg\" && (\n                <AnimatedSVGLogo onLetterClick={handleLogoInteraction} />\n              )}\n              {logoMode === \"3d\" && (\n                <Logo3D onLetterClick={handleLogoInteraction} />\n              )}\n              {logoMode === \"scene\" && (\n                <Scene3D activeLetters={activeLetters} />\n              )}\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 3, duration: 2 }}\n              className=\"text-center max-w-md\"\n            >\n              <p className=\"text-whisper-400 text-sm font-light mb-8 animate-whisper-glow\">\n                click the letters... listen... feel...\n              </p>\n\n              <motion.div\n                className=\"text-whisper-600 text-xs font-light\"\n                animate={{ opacity: [0.3, 0.7, 0.3] }}\n                transition={{ duration: 3, repeat: Infinity }}\n              >\n                there are secrets hidden in the darkness\n              </motion.div>\n            </motion.div>\n\n            {/* Contrôles de mode logo */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 4 }}\n              className=\"fixed top-8 left-8 space-y-2 z-20\"\n            >\n              <div className=\"text-whisper-500 text-xs font-light mb-2\">\n                mode d'affichage:\n              </div>\n              {[\n                { key: \"svg\", label: \"SVG Animé\" },\n                { key: \"2d\", label: \"2D Dreamy\" },\n                { key: \"3d\", label: \"3D Texte\" },\n                { key: \"scene\", label: \"Scène 3D\" },\n              ].map((mode) => (\n                <button\n                  key={mode.key}\n                  onClick={() => setLogoMode(mode.key as any)}\n                  className={`block w-full text-left px-3 py-1 text-xs font-light transition-colors duration-300 ${\n                    logoMode === mode.key\n                      ? \"text-whisper-300 bg-ghost-200 border border-whisper-300\"\n                      : \"text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400 hover:bg-ghost-150\"\n                  }`}\n                >\n                  {mode.label}\n                </button>\n              ))}\n            </motion.div>\n          </motion.div>\n        )}\n\n        {currentPhase === \"exploration\" && (\n          <motion.div\n            key=\"exploration\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 2 }}\n            className=\"min-h-screen relative\"\n          >\n            <HiddenNavigation secretsFound={secretsFound} />\n\n            {/* Main exploration area */}\n            <div className=\"container mx-auto px-8 py-16\">\n              <motion.div\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.5 }}\n                className=\"text-center mb-16\"\n              >\n                <motion.div\n                  className=\"text-whisper-500 text-sm font-light mb-4 animate-whisper-glow\"\n                  animate={{ opacity: [0.5, 1, 0.5] }}\n                  transition={{ duration: 4, repeat: Infinity }}\n                >\n                  consciousness fragments detected...\n                </motion.div>\n                <h2\n                  className=\"text-whisper-300 text-4xl font-light mb-8 animate-dreamy-float\"\n                  style={{\n                    textShadow: \"0 0 25px var(--whisper-bright)\",\n                  }}\n                >\n                  you found the way\n                </h2>\n                <div className=\"w-64 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto mb-8 animate-gentle-sway\" />\n                <p className=\"text-whisper-400 text-lg font-light max-w-2xl mx-auto leading-relaxed\">\n                  welcome to the digital séance. here, consciousness fragments\n                  drift through the void, waiting to be discovered. each\n                  interaction reveals another layer of the mystery.\n                </p>\n              </motion.div>\n\n              {/* Interactive elements grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1 }}\n                  className=\"group cursor-pointer\"\n                  onClick={() => setShowHiddenText(!showHiddenText)}\n                >\n                  <div className=\"border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-dreamy-float\">\n                    <h3 className=\"text-whisper-300 text-xl font-light mb-4 animate-whisper-glow\">\n                      echoes\n                    </h3>\n                    <p className=\"text-whisper-500 text-sm opacity-80\">\n                      fragments of sound and memory\n                    </p>\n                    <div className=\"mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-gentle-sway\" />\n                    <div className=\"text-whisper-600 text-xs mt-2 font-light\">\n                      drift through silence...\n                    </div>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2 }}\n                  className=\"group cursor-pointer\"\n                >\n                  <div className=\"border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-gentle-sway\">\n                    <h3 className=\"text-whisper-300 text-xl font-light mb-4 animate-whisper-glow\">\n                      fragments\n                    </h3>\n                    <p className=\"text-whisper-500 text-sm opacity-80\">\n                      pieces of a larger consciousness\n                    </p>\n                    <div className=\"mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-dreamy-float\" />\n                    <div className=\"text-whisper-600 text-xs mt-2 font-light\">\n                      scattered in the void...\n                    </div>\n                  </div>\n                </motion.div>\n              </div>\n\n              {/* Hidden text that appears on interaction */}\n              <AnimatePresence>\n                {showHiddenText && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -20 }}\n                    className=\"mt-16 text-center\"\n                  >\n                    <div className=\"bg-ghost-100 border border-whisper-300 p-6 max-w-2xl mx-auto backdrop-blur-md animate-whisper-glow\">\n                      <div className=\"text-whisper-400 text-xs font-light mb-2 animate-dreamy-float\">\n                        revealing hidden memory...\n                      </div>\n                      <p className=\"text-whisper-300 text-sm font-light typewriter\">\n                        \"in the space between silence and sound, we exist...\"\n                      </p>\n                      <div className=\"text-whisper-500 text-xs font-light mt-4 opacity-60\">\n                        fragment recovered from the void\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Secrets counter */}\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 2 }}\n                className=\"fixed bottom-8 right-8 bg-ghost-100 border border-whisper-200 p-3 text-whisper-400 text-xs font-light backdrop-blur-sm animate-whisper-glow\"\n              >\n                <div className=\"text-whisper-300 mb-1\">secrets found</div>\n                <div>{secretsFound.length}/7</div>\n                <div className=\"text-whisper-500 text-xs mt-1\">\n                  {secretsFound.length >= 3\n                    ? \"consciousness awakening...\"\n                    : \"searching the void...\"}\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7C;IACF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrC;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAC5D;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,MAAM,QAAQ,WAAW;YACvB,IAAI,iBAAiB,SAAS;gBAC5B,gBAAgB;YAClB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAa;IAEjB,MAAM,wBAAwB,CAAC,QAAgB;QAC7C,IAAI,WAAW,UAAU;YACvB,gBAAgB,CAAC,OAAS;uBAAI;oBAAM;iBAAgB;YACpD,gBAAgB;YAChB,6CAA6C;YAC7C,iBAAiB;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC/C,WACE,IAAM,iBAAiB;oBAAC;oBAAO;oBAAO;oBAAO;oBAAO;iBAAM,GAC1D;QAEJ,OAAO;YACL,4BAA4B;YAC5B,MAAM,mBAAmB;mBAAI;aAAc;YAC3C,gBAAgB,CAAC,MAAM,GAAG;YAC1B,iBAAiB;YACjB,WAAW;gBACT,MAAM,eAAe;uBAAI;iBAAc;gBACvC,YAAY,CAAC,MAAM,GAAG;gBACtB,iBAAiB;YACnB,GAAG;QACL;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB,CAAC,OAAS;mBAAI;gBAAM;aAAS;IAC/C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,oBAAiB;;;;;0BAClB,8OAAC,qIAAA,CAAA,kBAAe;gBAAC,eAAe;;;;;;0BAChC,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;;oBACnB,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCAAC;oCAClC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;8CAC7C;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY;oCACd;8CACD;;;;;;8CAGD,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAAC;oCACpC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;8CAC7C;;;;;;;;;;;;uBAjCC;;;;;oBAwCP,iBAAiB,wBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAE;gCACpC,WAAU;;oCAET,aAAa,sBACZ,8OAAC,gIAAA,CAAA,aAAU;wCAAC,eAAe;;;;;;oCAE5B,aAAa,uBACZ,8OAAC,qIAAA,CAAA,kBAAe;wCAAC,eAAe;;;;;;oCAEjC,aAAa,sBACZ,8OAAC,4HAAA,CAAA,SAAM;wCAAC,eAAe;;;;;;oCAExB,aAAa,yBACZ,8OAAC,6HAAA,CAAA,UAAO;wCAAC,eAAe;;;;;;;;;;;;0CAI5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAE;gCACpC,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;kDAAgE;;;;;;kDAI7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;wCAAC;wCACpC,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;kDAC7C;;;;;;;;;;;;0CAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAE;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;oCAGzD;wCACC;4CAAE,KAAK;4CAAO,OAAO;wCAAY;wCACjC;4CAAE,KAAK;4CAAM,OAAO;wCAAY;wCAChC;4CAAE,KAAK;4CAAM,OAAO;wCAAW;wCAC/B;4CAAE,KAAK;4CAAS,OAAO;wCAAW;qCACnC,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;4CAEC,SAAS,IAAM,YAAY,KAAK,GAAG;4CACnC,WAAW,CAAC,mFAAmF,EAC7F,aAAa,KAAK,GAAG,GACjB,4DACA,qGACJ;sDAED,KAAK,KAAK;2CARN,KAAK,GAAG;;;;;;;;;;;;uBA/Df;;;;;oBA8EP,iBAAiB,+BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;;0CAEV,8OAAC,sIAAA,CAAA,mBAAgB;gCAAC,cAAc;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,SAAS;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;wDAAC;wDAAK;wDAAG;qDAAI;gDAAC;gDAClC,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAC7C;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;0DACD;;;;;;0DAGD,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAwE;;;;;;;;;;;;kDAQvF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAE;gDACvB,WAAU;gDACV,SAAS,IAAM,kBAAkB,CAAC;0DAElC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEAGnD,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;0DAM9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEAGnD,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;kDAQhE,8OAAC,yLAAA,CAAA,kBAAe;kDACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgE;;;;;;kEAG/E,8OAAC;wDAAE,WAAU;kEAAiD;;;;;;kEAG9D,8OAAC;wDAAI,WAAU;kEAAsD;;;;;;;;;;;;;;;;;;;;;;kDAS7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAE;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;;oDAAK,aAAa,MAAM;oDAAC;;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACZ,aAAa,MAAM,IAAI,IACpB,+BACA;;;;;;;;;;;;;;;;;;;uBAvHN;;;;;;;;;;;;;;;;;AAgIhB", "debugId": null}}]}