'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface TerminalLine {
  id: number;
  text: string;
  type: 'command' | 'output' | 'error' | 'system';
  delay?: number;
}

interface LumonTerminalProps {
  onCommandComplete?: (command: string) => void;
}

export function LumonTerminal({ onCommandComplete }: LumonTerminalProps) {
  const [lines, setLines] = useState<TerminalLine[]>([]);
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);

  const bootSequence: TerminalLine[] = [
    { id: 1, text: 'LUMON INDUSTRIES MACRODATA REFINEMENT TERMINAL v2.1.4', type: 'system' },
    { id: 2, text: 'Initializing secure connection...', type: 'system', delay: 1000 },
    { id: 3, text: 'Connection established.', type: 'output', delay: 2000 },
    { id: 4, text: 'Loading artist consciousness data...', type: 'system', delay: 3000 },
    { id: 5, text: 'WARNING: Unauthorized emotional content detected', type: 'error', delay: 4000 },
    { id: 6, text: 'Bypassing wellness protocols...', type: 'system', delay: 5000 },
    { id: 7, text: 'Access granted to: how_r_u.exe', type: 'output', delay: 6000 },
    { id: 8, text: 'user@lumon:~$ ', type: 'command', delay: 7000 },
  ];

  const artistCommands = [
    'ls -la consciousness/',
    'cat memories/melancholic.txt',
    'grep -r "vulnerability" emotions/',
    'play audio/ambient_whispers.wav',
    'decrypt hidden/secrets.enc',
  ];

  useEffect(() => {
    const displayNextLine = () => {
      if (currentLineIndex < bootSequence.length) {
        const currentLine = bootSequence[currentLineIndex];
        
        setTimeout(() => {
          setLines(prev => [...prev, currentLine]);
          setCurrentLineIndex(prev => prev + 1);
        }, currentLine.delay || 500);
      } else {
        setIsTyping(false);
      }
    };

    if (currentLineIndex < bootSequence.length) {
      setIsTyping(true);
      displayNextLine();
    }
  }, [currentLineIndex]);

  const executeCommand = (command: string) => {
    const newLine: TerminalLine = {
      id: Date.now(),
      text: `user@lumon:~$ ${command}`,
      type: 'command'
    };
    
    setLines(prev => [...prev, newLine]);
    
    // Simulate command execution
    setTimeout(() => {
      let output = '';
      switch (command) {
        case 'help':
          output = 'Available commands: ls, cat, grep, play, decrypt, clear';
          break;
        case 'ls -la consciousness/':
          output = 'drwxr-xr-x fragments/\n-rw-r--r-- introspection.log\n-rw-r--r-- melancholy.dat';
          break;
        case 'cat memories/melancholic.txt':
          output = 'in the space between silence and sound, we exist...\nfragments of a larger consciousness drift through the void';
          break;
        default:
          output = `Command '${command}' executed. Data processed.`;
      }
      
      const outputLine: TerminalLine = {
        id: Date.now() + 1,
        text: output,
        type: 'output'
      };
      
      setLines(prev => [...prev, outputLine]);
      onCommandComplete?.(command);
    }, 1000 + Math.random() * 2000);
  };

  const getLineColor = (type: string) => {
    switch (type) {
      case 'command': return 'text-terminal-amber';
      case 'output': return 'text-terminal-green';
      case 'error': return 'text-terminal-red';
      case 'system': return 'text-terminal-blue';
      default: return 'text-terminal-green';
    }
  };

  return (
    <div className="bg-crt-black border-2 border-terminal-green p-6 font-mono text-sm relative overflow-hidden">
      {/* CRT Screen Effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-crt-glow to-transparent opacity-10 pointer-events-none animate-crt-flicker" />
      
      {/* Terminal Header */}
      <div className="flex justify-between items-center mb-4 border-b border-terminal-green pb-2">
        <span className="text-terminal-green">LUMON TERMINAL</span>
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-terminal-red rounded-full animate-pulse"></div>
          <div className="w-3 h-3 bg-terminal-amber rounded-full"></div>
          <div className="w-3 h-3 bg-terminal-green rounded-full animate-lumon-glow"></div>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="space-y-1 min-h-[400px] max-h-[400px] overflow-y-auto">
        <AnimatePresence>
          {lines.map((line, index) => (
            <motion.div
              key={line.id}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className={`${getLineColor(line.type)} whitespace-pre-wrap`}
            >
              {line.type === 'command' && (
                <span className="text-terminal-amber">user@lumon:~$ </span>
              )}
              <span className="animate-crt-flicker">
                {line.text.replace('user@lumon:~$ ', '')}
              </span>
              {index === lines.length - 1 && line.type === 'command' && (
                <span className="animate-terminal-blink">_</span>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Interactive Command Buttons */}
        {!isTyping && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="mt-6 space-y-2"
          >
            <div className="text-terminal-green text-xs mb-2">
              Available commands (click to execute):
            </div>
            {artistCommands.map((command, index) => (
              <motion.button
                key={command}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1 + index * 0.2 }}
                onClick={() => executeCommand(command)}
                className="block text-left text-terminal-amber hover:text-terminal-green hover:bg-crt-dark-gray px-2 py-1 rounded transition-colors duration-300 text-xs animate-corporate-slide"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                &gt; {command}
              </motion.button>
            ))}
          </motion.div>
        )}
      </div>

      {/* Status Bar */}
      <div className="border-t border-terminal-green pt-2 mt-4 flex justify-between text-xs">
        <span className="text-terminal-green animate-crt-flicker">
          STATUS: CONNECTED
        </span>
        <span className="text-terminal-amber">
          SECURITY: BYPASSED
        </span>
        <span className="text-terminal-blue">
          WELLNESS: DISABLED
        </span>
      </div>
    </div>
  );
}
