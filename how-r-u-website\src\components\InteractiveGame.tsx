"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface GameState {
  level: number;
  score: number;
  lives: number;
  sequence: number[];
  playerSequence: number[];
  isPlaying: boolean;
  isShowingSequence: boolean;
  gameOver: boolean;
  victory: boolean;
}

interface InteractiveGameProps {
  onGameComplete?: (score: number) => void;
  onLetterActivate?: (index: number) => void;
}

export function InteractiveGame({
  onGameComplete,
  onLetterActivate,
}: InteractiveGameProps) {
  const [gameState, setGameState] = useState<GameState>({
    level: 1,
    score: 0,
    lives: 3,
    sequence: [],
    playerSequence: [],
    isPlaying: false,
    isShowingSequence: false,
    gameOver: false,
    victory: false,
  });

  const [activeButton, setActiveButton] = useState<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const letters = ["h", "o", "w", "r", "u"];
  const colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57"];

  const generateSequence = useCallback((level: number) => {
    const sequence = [];
    for (let i = 0; i < level + 2; i++) {
      sequence.push(Math.floor(Math.random() * 5));
    }
    return sequence;
  }, []);

  const startGame = () => {
    const newSequence = generateSequence(1);
    setGameState({
      level: 1,
      score: 0,
      lives: 3,
      sequence: newSequence,
      playerSequence: [],
      isPlaying: true,
      isShowingSequence: true,
      gameOver: false,
      victory: false,
    });
    showSequence(newSequence);
  };

  const showSequence = async (sequence: number[]) => {
    setGameState((prev) => ({ ...prev, isShowingSequence: true }));

    for (let i = 0; i < sequence.length; i++) {
      await new Promise((resolve) => setTimeout(resolve, 600));
      setActiveButton(sequence[i]);
      onLetterActivate?.(sequence[i]);

      await new Promise((resolve) => setTimeout(resolve, 400));
      setActiveButton(null);
    }

    setGameState((prev) => ({ ...prev, isShowingSequence: false }));
  };

  const handleButtonClick = (index: number) => {
    if (gameState.isShowingSequence || gameState.gameOver || gameState.victory)
      return;

    const newPlayerSequence = [...gameState.playerSequence, index];
    setActiveButton(index);
    onLetterActivate?.(index);

    setTimeout(() => setActiveButton(null), 200);

    // Vérifier si le joueur a fait une erreur
    if (
      newPlayerSequence[newPlayerSequence.length - 1] !==
      gameState.sequence[newPlayerSequence.length - 1]
    ) {
      // Erreur !
      const newLives = gameState.lives - 1;
      if (newLives <= 0) {
        setGameState((prev) => ({ ...prev, gameOver: true, isPlaying: false }));
      } else {
        setGameState((prev) => ({
          ...prev,
          lives: newLives,
          playerSequence: [],
          isShowingSequence: true,
        }));
        setTimeout(() => showSequence(gameState.sequence), 1000);
      }
      return;
    }

    // Vérifier si la séquence est complète
    if (newPlayerSequence.length === gameState.sequence.length) {
      const newScore = gameState.score + gameState.level * 100;
      const newLevel = gameState.level + 1;

      if (newLevel > 10) {
        // Victoire !
        setGameState((prev) => ({
          ...prev,
          victory: true,
          isPlaying: false,
          score: newScore,
        }));
        onGameComplete?.(newScore);
      } else {
        // Niveau suivant
        const newSequence = generateSequence(newLevel);
        setGameState((prev) => ({
          ...prev,
          level: newLevel,
          score: newScore,
          sequence: newSequence,
          playerSequence: [],
          isShowingSequence: true,
        }));
        setTimeout(() => showSequence(newSequence), 1500);
      }
    } else {
      setGameState((prev) => ({ ...prev, playerSequence: newPlayerSequence }));
    }
  };

  const resetGame = () => {
    setGameState({
      level: 1,
      score: 0,
      lives: 3,
      sequence: [],
      playerSequence: [],
      isPlaying: false,
      isShowingSequence: false,
      gameOver: false,
      victory: false,
    });
  };

  return (
    <div
      className={`w-full max-w-2xl mx-auto p-6 ${isMobile ? "px-4" : "px-6"}`}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h2 className="text-whisper-300 text-2xl font-light mb-4">
          Jeu de Mémoire "how r u"
        </h2>
        <p className="text-whisper-500 text-sm font-light mb-6">
          Mémorise et reproduis la séquence de lettres
        </p>

        {/* Stats du jeu */}
        <div
          className={`flex ${
            isMobile ? "flex-col space-y-2" : "justify-center space-x-8"
          } mb-6`}
        >
          <div className="text-whisper-400">
            <span className="text-whisper-300">Niveau:</span> {gameState.level}
          </div>
          <div className="text-whisper-400">
            <span className="text-whisper-300">Score:</span> {gameState.score}
          </div>
          <div className="text-whisper-400">
            <span className="text-whisper-300">Vies:</span>
            {Array.from({ length: gameState.lives }).map((_, i) => (
              <span key={i} className="text-red-400 ml-1">
                ♥
              </span>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Boutons de lettres */}
      <div
        className={`grid ${
          isMobile
            ? "grid-cols-2 gap-4 max-w-xs mx-auto"
            : "grid-cols-5 gap-6 max-w-2xl mx-auto"
        } mb-8`}
      >
        {letters.map((letter, index) => (
          <motion.button
            key={letter}
            onClick={() => handleButtonClick(index)}
            disabled={
              gameState.isShowingSequence ||
              gameState.gameOver ||
              gameState.victory
            }
            className={`
              ${isMobile ? "h-20 w-20 text-lg" : "h-24 w-24 text-2xl"}
              rounded-xl border-3 font-mono font-bold
              transition-all duration-300 transform backdrop-blur-sm
              ${
                activeButton === index
                  ? "scale-110 shadow-2xl"
                  : "hover:scale-105"
              }
              ${
                gameState.isShowingSequence ||
                gameState.gameOver ||
                gameState.victory
                  ? "opacity-40 cursor-not-allowed"
                  : "cursor-pointer hover:shadow-lg"
              }
            `}
            style={{
              backgroundColor:
                activeButton === index
                  ? colors[index]
                  : "rgba(255,255,255,0.05)",
              borderColor: colors[index],
              color: activeButton === index ? "#000" : colors[index],
              boxShadow:
                activeButton === index ? `0 0 30px ${colors[index]}` : "none",
            }}
            whileTap={{ scale: 0.9 }}
            whileHover={{
              boxShadow: `0 0 20px ${colors[index]}40`,
              borderWidth: "3px",
            }}
          >
            {letter}
          </motion.button>
        ))}
        {/* Bouton central pour mobile */}
        {isMobile && (
          <motion.div
            className="col-span-2 flex justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <motion.button
              onClick={() => handleButtonClick(4)}
              disabled={
                gameState.isShowingSequence ||
                gameState.gameOver ||
                gameState.victory
              }
              className={`
                h-20 w-20 text-lg rounded-xl border-3 font-mono font-bold
                transition-all duration-300 transform backdrop-blur-sm
                ${
                  activeButton === 4
                    ? "scale-110 shadow-2xl"
                    : "hover:scale-105"
                }
                ${
                  gameState.isShowingSequence ||
                  gameState.gameOver ||
                  gameState.victory
                    ? "opacity-40 cursor-not-allowed"
                    : "cursor-pointer hover:shadow-lg"
                }
              `}
              style={{
                backgroundColor:
                  activeButton === 4 ? colors[4] : "rgba(255,255,255,0.05)",
                borderColor: colors[4],
                color: activeButton === 4 ? "#000" : colors[4],
                boxShadow:
                  activeButton === 4 ? `0 0 30px ${colors[4]}` : "none",
              }}
              whileTap={{ scale: 0.9 }}
            >
              {letters[4]}
            </motion.button>
          </motion.div>
        )}
      </div>

      {/* Messages de statut */}
      <AnimatePresence>
        {gameState.isShowingSequence && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center text-whisper-400 mb-4"
          >
            Mémorise la séquence...
          </motion.div>
        )}

        {gameState.gameOver && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center mb-4"
          >
            <div className="text-red-400 text-xl mb-2">Game Over</div>
            <div className="text-whisper-500 text-sm">
              Score final: {gameState.score}
            </div>
          </motion.div>
        )}

        {gameState.victory && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center mb-4"
          >
            <div className="text-green-400 text-xl mb-2">Victoire ! 🎉</div>
            <div className="text-whisper-500 text-sm">
              Score final: {gameState.score}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Boutons de contrôle */}
      <div className="text-center space-y-4">
        {!gameState.isPlaying && !gameState.gameOver && !gameState.victory && (
          <motion.button
            onClick={startGame}
            className="px-6 py-3 bg-ghost-100 border border-whisper-200 text-whisper-300 font-light hover:bg-ghost-200 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Commencer le Jeu
          </motion.button>
        )}

        {(gameState.gameOver || gameState.victory) && (
          <motion.button
            onClick={resetGame}
            className="px-6 py-3 bg-ghost-100 border border-whisper-200 text-whisper-300 font-light hover:bg-ghost-200 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Rejouer
          </motion.button>
        )}
      </div>

      {/* Instructions */}
      {!gameState.isPlaying && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center mt-8 text-whisper-600 text-xs font-light"
        >
          <p>
            Regarde la séquence, puis reproduis-la en cliquant sur les lettres
          </p>
          <p className="mt-1">10 niveaux à compléter pour gagner !</p>
        </motion.div>
      )}
    </div>
  );
}
