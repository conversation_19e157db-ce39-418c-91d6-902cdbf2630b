{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/objectSpread2-284232a6.esm.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nexport { _objectSpread2 as _, _defineProperty as a };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACtC,IAAI,OAAO,KAAK;QACd,OAAO,cAAc,CAAC,KAAK,KAAK;YAC9B,OAAO;YACP,YAAY;YACZ,cAAc;YACd,UAAU;QACZ;IACF,OAAO;QACL,GAAG,CAAC,IAAI,GAAG;IACb;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,MAAM,EAAE,cAAc;IACrC,IAAI,OAAO,OAAO,IAAI,CAAC;IAEvB,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAE3C,IAAI,gBAAgB;YAClB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG;gBACpC,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;YAChE;QACF;QAEA,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IACxB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;QAEpD,IAAI,IAAI,GAAG;YACT,QAAQ,OAAO,SAAS,MAAM,OAAO,CAAC,SAAU,GAAG;gBACjD,gBAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;YAC1C;QACF,OAAO,IAAI,OAAO,yBAAyB,EAAE;YAC3C,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;QACnE,OAAO;YACL,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG;gBAC3C,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;YAC7E;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/isNativeReflectConstruct-5594d075.esm.js"], "sourcesContent": ["function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport { _setPrototypeOf as _, _isNativeReflectConstruct as a };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,kBAAkB,OAAO,cAAc,IAAI,SAAS,gBAAgB,CAAC,EAAE,CAAC;QACtE,EAAE,SAAS,GAAG;QACd,OAAO;IACT;IAEA,OAAO,gBAAgB,GAAG;AAC5B;AAEA,SAAS;IACP,IAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,SAAS,EAAE,OAAO;IACjE,IAAI,QAAQ,SAAS,CAAC,IAAI,EAAE,OAAO;IACnC,IAAI,OAAO,UAAU,YAAY,OAAO;IAExC,IAAI;QACF,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;QAC3E,OAAO;IACT,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/matrix-baa530bf.esm.js"], "sourcesContent": ["import { Matrix3 } from 'three';\n\n/**\n *\n * @param terms\n *\n * | a b |\n * | c d |\n *\n * @returns {number} determinant\n */\n\nfunction determinant2() {\n  for (var _len = arguments.length, terms = new Array(_len), _key = 0; _key < _len; _key++) {\n    terms[_key] = arguments[_key];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3];\n  return a * d - b * c;\n}\n/**\n *\n * @param terms\n *\n * | a b c |\n * | d e f |\n * | g h i |\n *\n * @returns {number} determinant\n */\n\nfunction determinant3() {\n  for (var _len2 = arguments.length, terms = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    terms[_key2] = arguments[_key2];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3],\n      e = terms[4],\n      f = terms[5],\n      g = terms[6],\n      h = terms[7],\n      i = terms[8];\n  return a * e * i + b * f * g + c * d * h - c * e * g - b * d * i - a * f * h;\n}\n/**\n *\n * @param terms\n *\n * | a b c g |\n * | h i j k |\n * | l m n o |\n *\n * @returns {number} determinant\n */\n\nfunction determinant4() {\n  for (var _len3 = arguments.length, terms = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    terms[_key3] = arguments[_key3];\n  }\n\n  terms[0];\n      terms[1];\n      terms[2];\n      terms[3];\n      terms[4];\n      terms[5];\n      terms[6];\n      terms[7];\n      terms[8];\n      terms[9];\n      terms[10];\n      terms[11];\n      terms[12];\n      terms[13];\n      terms[14]; // TODO\n}\n/**\n *\n * Get the determinant of matrix m without row r and col c\n *\n * @param {matrix} m Starter matrix\n * @param r row to remove\n * @param c col to remove\n *\n *     | a b c |\n * m = | d e f |\n *     | g h i |\n *\n * getMinor(m, 1, 1) would result in this determinant\n *\n * | a c |\n * | g i |\n *\n * @returns {number} determinant\n */\n\nfunction getMinor(matrix, r, c) {\n  var _matrixTranspose = matrix.clone().transpose();\n\n  var x = [];\n  var l = _matrixTranspose.elements.length;\n  var n = Math.sqrt(l);\n\n  for (var i = 0; i < l; i++) {\n    var element = _matrixTranspose.elements[i];\n    var row = Math.floor(i / n);\n    var col = i % n;\n\n    if (row !== r - 1 && col !== c - 1) {\n      x.push(element);\n    }\n  }\n\n  return determinant3.apply(void 0, x);\n}\n/**\n *\n */\n\nfunction matrixSum3(m1, m2) {\n  var sum = [];\n  var m1Array = m1.toArray();\n  var m2Array = m2.toArray();\n\n  for (var i = 0; i < m1Array.length; i++) {\n    sum[i] = m1Array[i] + m2Array[i];\n  }\n\n  return new Matrix3().fromArray(sum);\n}\n\nvar matrix = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  determinant2: determinant2,\n  determinant3: determinant3,\n  determinant4: determinant4,\n  getMinor: getMinor,\n  matrixSum3: matrixSum3\n});\n\nexport { matrixSum3 as a, determinant2 as b, determinant4 as c, determinant3 as d, getMinor as g, matrix as m };\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA;;;;;;;;CAQC,GAED,SAAS;IACP,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACxF,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC/B;IAEA,IAAI,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE;IAChB,OAAO,IAAI,IAAI,IAAI;AACrB;AACA;;;;;;;;;CASC,GAED,SAAS;IACP,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;QAC9F,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;IACjC;IAEA,IAAI,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE,EACZ,IAAI,KAAK,CAAC,EAAE;IAChB,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7E;AACA;;;;;;;;;CASC,GAED,SAAS;IACP,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;QAC9F,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;IACjC;IAEA,KAAK,CAAC,EAAE;IACJ,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,EAAE;IACR,KAAK,CAAC,GAAG;IACT,KAAK,CAAC,GAAG;IACT,KAAK,CAAC,GAAG;IACT,KAAK,CAAC,GAAG;IACT,KAAK,CAAC,GAAG,EAAE,OAAO;AACxB;AACA;;;;;;;;;;;;;;;;;;CAkBC,GAED,SAAS,SAAS,MAAM,EAAE,CAAC,EAAE,CAAC;IAC5B,IAAI,mBAAmB,OAAO,KAAK,GAAG,SAAS;IAE/C,IAAI,IAAI,EAAE;IACV,IAAI,IAAI,iBAAiB,QAAQ,CAAC,MAAM;IACxC,IAAI,IAAI,KAAK,IAAI,CAAC;IAElB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,IAAI,UAAU,iBAAiB,QAAQ,CAAC,EAAE;QAC1C,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI;QACzB,IAAI,MAAM,IAAI;QAEd,IAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG;YAClC,EAAE,IAAI,CAAC;QACT;IACF;IAEA,OAAO,aAAa,KAAK,CAAC,KAAK,GAAG;AACpC;AACA;;CAEC,GAED,SAAS,WAAW,EAAE,EAAE,EAAE;IACxB,IAAI,MAAM,EAAE;IACZ,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IAExB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IAClC;IAEA,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;AACjC;AAEA,IAAI,SAAS,WAAW,GAAE,OAAO,MAAM,CAAC;IACtC,WAAW;IACX,cAAc;IACd,cAAc;IACd,cAAc;IACd,UAAU;IACV,YAAY;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/triangle-b62b9067.esm.js"], "sourcesContent": ["import { a as _isNativeReflectConstruct, _ as _setPrototypeOf } from './isNativeReflectConstruct-5594d075.esm.js';\nimport { Vector2, Matrix4 } from 'three';\nimport { d as determinant3, g as getMinor } from './matrix-baa530bf.esm.js';\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}\n\n/**\n *\n * @param point\n *\n * @param triangle\n *\n * @returns {boolean} true if the point is in the triangle\n *\n * TODO: Find explainer\n */\nfunction isPointInTriangle(point, triangle) {\n  var _triangle$ = _slicedToArray(triangle[0], 2),\n      ax = _triangle$[0],\n      ay = _triangle$[1];\n\n  var _triangle$2 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$2[0],\n      by = _triangle$2[1];\n\n  var _triangle$3 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$3[0],\n      cy = _triangle$3[1];\n\n  var _point = _slicedToArray(point, 2),\n      px = _point[0],\n      py = _point[1]; // TODO Sub with static calc\n\n\n  var matrix = new Matrix4(); // prettier-ignore\n\n  matrix.set(ax, ay, ax * ax + ay * ay, 1, bx, by, bx * bx + by * by, 1, cx, cy, cx * cx + cy * cy, 1, px, py, px * px + py * py, 1);\n  return matrix.determinant() <= 0;\n}\nfunction triangleDeterminant(triangle) {\n  var _triangle$4 = _slicedToArray(triangle[0], 2),\n      x1 = _triangle$4[0],\n      y1 = _triangle$4[1];\n\n  var _triangle$5 = _slicedToArray(triangle[1], 2),\n      x2 = _triangle$5[0],\n      y2 = _triangle$5[1];\n\n  var _triangle$6 = _slicedToArray(triangle[2], 2),\n      x3 = _triangle$6[0],\n      y3 = _triangle$6[1]; // prettier-ignore\n\n\n  return determinant3(x1, y1, 1, x2, y2, 1, x3, y3, 1);\n}\n/**\n * Uses triangle area determinant to check if 3 points are collinear.\n * If they are, they can't make a triangle, so the determinant will be 0!\n *\n *      0     1     2\n * ─────■─────■─────■\n *\n *\n * Fun fact, you can use this same determinant to check the order of the points in the triangle\n *\n * NOTE: Should this use a buffer instead? NOTE: Should this use a buffer instead? [x0, y0, x1, y1, x2, y2]?\n *\n */\n\nfunction arePointsCollinear(points) {\n  return triangleDeterminant(points) === 0;\n} // TODO This is the same principle as the prev function, find a way to make it have sense\n\nfunction isTriangleClockwise(triangle) {\n  return triangleDeterminant(triangle) < 0;\n}\n/**\n \nThe circumcircle is a circle touching all the vertices of a triangle or polygon.\n\n             ┌───┐             \n             │ B │             \n             └───┘             \n           .───●───.           \n        ,─'   ╱ ╲   '─.        \n      ,'     ╱   ╲     `.      \n     ╱      ╱     ╲      ╲     \n    ;      ╱       ╲      :    \n    │     ╱         ╲     │    \n    │    ╱           ╲    │    \n    :   ╱             ╲   ;    \n     ╲ ╱               ╲ ╱     \n┌───┐ ●─────────────────● ┌───┐\n│ A │  `.             ,'  │ C │\n└───┘    '─.       ,─'    └───┘\n            `─────'                         \n */\n\n/**\n *\n * @param triangle\n *\n * @returns {number} circumcircle\n */\n// https://math.stackexchange.com/a/1460096\n\nfunction getCircumcircle(triangle) {\n  // TS-TODO the next few lines are ignored because the types aren't current to the change in vectors (that can now be iterated)\n  // @ts-ignore\n  var _triangle$7 = _slicedToArray(triangle[0], 2),\n      ax = _triangle$7[0],\n      ay = _triangle$7[1]; // @ts-ignore\n\n\n  var _triangle$8 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$8[0],\n      by = _triangle$8[1]; // @ts-ignore\n\n\n  var _triangle$9 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$9[0],\n      cy = _triangle$9[1];\n\n  if (arePointsCollinear(triangle)) return null; // points are collinear\n\n  var m = new Matrix4(); // prettier-ignore\n\n  m.set(1, 1, 1, 1, ax * ax + ay * ay, ax, ay, 1, bx * bx + by * by, bx, by, 1, cx * cx + cy * cy, cx, cy, 1);\n  var m11 = getMinor(m, 1, 1);\n  var m13 = getMinor(m, 1, 3);\n  var m12 = getMinor(m, 1, 2);\n  var m14 = getMinor(m, 1, 4);\n  var x0 = 0.5 * (m12 / m11);\n  var y0 = 0.5 * (m13 / m11);\n  var r2 = x0 * x0 + y0 * y0 + m14 / m11;\n  return {\n    x: Math.abs(x0) === 0 ? 0 : x0,\n    y: Math.abs(y0) === 0 ? 0 : -y0,\n    r: Math.sqrt(r2)\n  };\n} // https://stackoverflow.com/questions/39984709/how-can-i-check-wether-a-point-is-inside-the-circumcircle-of-3-points\n\nfunction isPointInCircumcircle(point, triangle) {\n  var _ref = Array.isArray(triangle[0]) ? triangle[0] : triangle[0].toArray(),\n      _ref2 = _slicedToArray(_ref, 2),\n      ax = _ref2[0],\n      ay = _ref2[1];\n\n  var _ref3 = Array.isArray(triangle[1]) ? triangle[1] : triangle[1].toArray(),\n      _ref4 = _slicedToArray(_ref3, 2),\n      bx = _ref4[0],\n      by = _ref4[1];\n\n  var _ref5 = Array.isArray(triangle[2]) ? triangle[2] : triangle[2].toArray(),\n      _ref6 = _slicedToArray(_ref5, 2),\n      cx = _ref6[0],\n      cy = _ref6[1];\n\n  var _point2 = _slicedToArray(point, 2),\n      px = _point2[0],\n      py = _point2[1];\n\n  if (arePointsCollinear(triangle)) throw new Error(\"Collinear points don't form a triangle\");\n  /**\n          | ax-px, ay-py, (ax-px)² + (ay-py)² |\n    det = | bx-px, by-py, (bx-px)² + (by-py)² |\n          | cx-px, cy-py, (cx-px)² + (cy-py)² |\n  */\n\n  var x1mpx = ax - px;\n  var aympy = ay - py;\n  var bxmpx = bx - px;\n  var bympy = by - py;\n  var cxmpx = cx - px;\n  var cympy = cy - py; // prettier-ignore\n\n  var d = determinant3(x1mpx, aympy, x1mpx * x1mpx + aympy * aympy, bxmpx, bympy, bxmpx * bxmpx + bympy * bympy, cxmpx, cympy, cxmpx * cxmpx + cympy * cympy); // if d is 0, the point is on C\n\n  if (d === 0) {\n    return true;\n  }\n\n  return !isTriangleClockwise(triangle) ? d > 0 : d < 0;\n} // From https://algorithmtutor.com/Computational-Geometry/Determining-if-two-consecutive-segments-turn-left-or-right/\n\nvar mv1 = new Vector2();\nvar mv2 = new Vector2();\n/**\n \n     ╱      ╲     \n    ╱        ╲    \n   ▕          ▏   \n                  \n right      left  \n\n * NOTE: Should this use a buffer instead? [x0, y0, x1, y1]?\n */\n\nfunction doThreePointsMakeARight(points) {\n  var _points$map = points.map(function (p) {\n    if (Array.isArray(p)) {\n      return _construct(Vector2, _toConsumableArray(p));\n    }\n\n    return p;\n  }),\n      _points$map2 = _slicedToArray(_points$map, 3),\n      p1 = _points$map2[0],\n      p2 = _points$map2[1],\n      p3 = _points$map2[2];\n\n  if (arePointsCollinear(points)) return false; // @ts-ignore\n\n  var p2p1 = mv1.subVectors(p2, p1); // @ts-ignore\n\n  var p3p1 = mv2.subVectors(p3, p1);\n  var cross = p3p1.cross(p2p1);\n  return cross > 0;\n}\n\nvar triangle = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  isPointInTriangle: isPointInTriangle,\n  triangleDeterminant: triangleDeterminant,\n  arePointsCollinear: arePointsCollinear,\n  isTriangleClockwise: isTriangleClockwise,\n  getCircumcircle: getCircumcircle,\n  isPointInCircumcircle: isPointInCircumcircle,\n  doThreePointsMakeARight: doThreePointsMakeARight\n});\n\nexport { _slicedToArray as _, _toConsumableArray as a, triangleDeterminant as b, arePointsCollinear as c, doThreePointsMakeARight as d, isTriangleClockwise as e, isPointInCircumcircle as f, getCircumcircle as g, isPointInTriangle as i, triangle as t };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,SAAS,gBAAgB,GAAG;IAC1B,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO;AACjC;AAEA,SAAS,sBAAsB,GAAG,EAAE,CAAC;IACnC,IAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,GAAG,CAAC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,aAAa;IAExG,IAAI,MAAM,MAAM;IAChB,IAAI,OAAO,EAAE;IACb,IAAI,KAAK;IACT,IAAI,KAAK;IAET,IAAI,IAAI;IAER,IAAI;QACF,IAAK,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,EAAE,EAAE,IAAI,GAAG,KAAK,KAAM;YAChE,KAAK,IAAI,CAAC,GAAG,KAAK;YAElB,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG;QAC9B;IACF,EAAE,OAAO,KAAK;QACZ,KAAK;QACL,KAAK;IACP,SAAU;QACR,IAAI;YACF,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC,SAAS;QAC/C,SAAU;YACR,IAAI,IAAI,MAAM;QAChB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,GAAG,EAAE,GAAG;IACjC,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,MAAM;IAErD,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;IAErE,OAAO;AACT;AAEA,SAAS,4BAA4B,CAAC,EAAE,MAAM;IAC5C,IAAI,CAAC,GAAG;IACR,IAAI,OAAO,MAAM,UAAU,OAAO,kBAAkB,GAAG;IACvD,IAAI,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IACpD,IAAI,MAAM,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;IAC3D,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;IAClD,IAAI,MAAM,eAAe,2CAA2C,IAAI,CAAC,IAAI,OAAO,kBAAkB,GAAG;AAC3G;AAEA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB;AAEA,SAAS,eAAe,GAAG,EAAE,CAAC;IAC5B,OAAO,gBAAgB,QAAQ,sBAAsB,KAAK,MAAM,4BAA4B,KAAK,MAAM;AACzG;AAEA,SAAS,mBAAmB,GAAG;IAC7B,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,kBAAkB;AACnD;AAEA,SAAS,iBAAiB,IAAI;IAC5B,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,aAAa,IAAI,MAAM,OAAO,MAAM,IAAI,CAAC;AACtH;AAEA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB;AAEA,SAAS,mBAAmB,GAAG;IAC7B,OAAO,mBAAmB,QAAQ,iBAAiB,QAAQ,4BAA4B,QAAQ;AACjG;AAEA,SAAS,WAAW,MAAM,EAAE,IAAI,EAAE,KAAK;IACrC,IAAI,CAAA,GAAA,kOAAA,CAAA,IAAyB,AAAD,KAAK;QAC/B,aAAa,QAAQ,SAAS;IAChC,OAAO;QACL,aAAa,SAAS,WAAW,MAAM,EAAE,IAAI,EAAE,KAAK;YAClD,IAAI,IAAI;gBAAC;aAAK;YACd,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;YAChB,IAAI,cAAc,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC9C,IAAI,WAAW,IAAI;YACnB,IAAI,OAAO,CAAA,GAAA,kOAAA,CAAA,IAAe,AAAD,EAAE,UAAU,MAAM,SAAS;YACpD,OAAO;QACT;IACF;IAEA,OAAO,WAAW,KAAK,CAAC,MAAM;AAChC;AAEA;;;;;;;;;CASC,GACD,SAAS,kBAAkB,KAAK,EAAE,QAAQ;IACxC,IAAI,aAAa,eAAe,QAAQ,CAAC,EAAE,EAAE,IACzC,KAAK,UAAU,CAAC,EAAE,EAClB,KAAK,UAAU,CAAC,EAAE;IAEtB,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE;IAEvB,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE;IAEvB,IAAI,SAAS,eAAe,OAAO,IAC/B,KAAK,MAAM,CAAC,EAAE,EACd,KAAK,MAAM,CAAC,EAAE,EAAE,4BAA4B;IAGhD,IAAI,SAAS,IAAI,+IAAA,CAAA,UAAO,IAAI,kBAAkB;IAE9C,OAAO,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI;IAChI,OAAO,OAAO,WAAW,MAAM;AACjC;AACA,SAAS,oBAAoB,QAAQ;IACnC,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE;IAEvB,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE;IAEvB,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE,EAAE,kBAAkB;IAG3C,OAAO,CAAA,GAAA,gNAAA,CAAA,IAAY,AAAD,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI;AACpD;AACA;;;;;;;;;;;;CAYC,GAED,SAAS,mBAAmB,MAAM;IAChC,OAAO,oBAAoB,YAAY;AACzC,EAAE,yFAAyF;AAE3F,SAAS,oBAAoB,QAAQ;IACnC,OAAO,oBAAoB,YAAY;AACzC;AACA;;;;;;;;;;;;;;;;;;;;CAoBC,GAED;;;;;CAKC,GACD,2CAA2C;AAE3C,SAAS,gBAAgB,QAAQ;IAC/B,8HAA8H;IAC9H,aAAa;IACb,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE,EAAE,aAAa;IAGtC,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE,EAAE,aAAa;IAGtC,IAAI,cAAc,eAAe,QAAQ,CAAC,EAAE,EAAE,IAC1C,KAAK,WAAW,CAAC,EAAE,EACnB,KAAK,WAAW,CAAC,EAAE;IAEvB,IAAI,mBAAmB,WAAW,OAAO,MAAM,uBAAuB;IAEtE,IAAI,IAAI,IAAI,+IAAA,CAAA,UAAO,IAAI,kBAAkB;IAEzC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI;IACzG,IAAI,MAAM,CAAA,GAAA,gNAAA,CAAA,IAAQ,AAAD,EAAE,GAAG,GAAG;IACzB,IAAI,MAAM,CAAA,GAAA,gNAAA,CAAA,IAAQ,AAAD,EAAE,GAAG,GAAG;IACzB,IAAI,MAAM,CAAA,GAAA,gNAAA,CAAA,IAAQ,AAAD,EAAE,GAAG,GAAG;IACzB,IAAI,MAAM,CAAA,GAAA,gNAAA,CAAA,IAAQ,AAAD,EAAE,GAAG,GAAG;IACzB,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG;IACzB,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG;IACzB,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;IACnC,OAAO;QACL,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,IAAI;QAC5B,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC;QAC7B,GAAG,KAAK,IAAI,CAAC;IACf;AACF,EAAE,qHAAqH;AAEvH,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IAC5C,IAAI,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,IACrE,QAAQ,eAAe,MAAM,IAC7B,KAAK,KAAK,CAAC,EAAE,EACb,KAAK,KAAK,CAAC,EAAE;IAEjB,IAAI,QAAQ,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,IACtE,QAAQ,eAAe,OAAO,IAC9B,KAAK,KAAK,CAAC,EAAE,EACb,KAAK,KAAK,CAAC,EAAE;IAEjB,IAAI,QAAQ,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,IACtE,QAAQ,eAAe,OAAO,IAC9B,KAAK,KAAK,CAAC,EAAE,EACb,KAAK,KAAK,CAAC,EAAE;IAEjB,IAAI,UAAU,eAAe,OAAO,IAChC,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,OAAO,CAAC,EAAE;IAEnB,IAAI,mBAAmB,WAAW,MAAM,IAAI,MAAM;IAClD;;;;EAIA,GAEA,IAAI,QAAQ,KAAK;IACjB,IAAI,QAAQ,KAAK;IACjB,IAAI,QAAQ,KAAK;IACjB,IAAI,QAAQ,KAAK;IACjB,IAAI,QAAQ,KAAK;IACjB,IAAI,QAAQ,KAAK,IAAI,kBAAkB;IAEvC,IAAI,IAAI,CAAA,GAAA,gNAAA,CAAA,IAAY,AAAD,EAAE,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,+BAA+B;IAE5L,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IAEA,OAAO,CAAC,oBAAoB,YAAY,IAAI,IAAI,IAAI;AACtD,EAAE,qHAAqH;AAEvH,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAO;AACrB,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAO;AACrB;;;;;;;;;CASC,GAED,SAAS,wBAAwB,MAAM;IACrC,IAAI,cAAc,OAAO,GAAG,CAAC,SAAU,CAAC;QACtC,IAAI,MAAM,OAAO,CAAC,IAAI;YACpB,OAAO,WAAW,+IAAA,CAAA,UAAO,EAAE,mBAAmB;QAChD;QAEA,OAAO;IACT,IACI,eAAe,eAAe,aAAa,IAC3C,KAAK,YAAY,CAAC,EAAE,EACpB,KAAK,YAAY,CAAC,EAAE,EACpB,KAAK,YAAY,CAAC,EAAE;IAExB,IAAI,mBAAmB,SAAS,OAAO,OAAO,aAAa;IAE3D,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa;IAEhD,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI;IAC9B,IAAI,QAAQ,KAAK,KAAK,CAAC;IACvB,OAAO,QAAQ;AACjB;AAEA,IAAI,WAAW,WAAW,GAAE,OAAO,MAAM,CAAC;IACxC,WAAW;IACX,mBAAmB;IACnB,qBAAqB;IACrB,oBAAoB;IACpB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,yBAAyB;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/misc-7d870b3c.esm.js"], "sourcesContent": ["import { d as doThreePointsMakeARight, a as _toConsumableArray, _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Vector3, Matrix3 } from 'three';\nimport { a as matrixSum3 } from './matrix-baa530bf.esm.js';\n\n/**\n * Clamps a value between a range.\n */\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n} // Loops the value t, so that it is never larger than length and never smaller than 0.\n\nfunction repeat(t, length) {\n  return clamp(t - Math.floor(t / length) * length, 0, length);\n} // Calculates the shortest difference between two given angles.\n\n\nfunction deltaAngle(current, target) {\n  var delta = repeat(target - current, Math.PI * 2);\n  if (delta > Math.PI) delta -= Math.PI * 2;\n  return delta;\n}\n/**\n * Converts degrees to radians.\n */\n\nfunction degToRad(degrees) {\n  return degrees / 180 * Math.PI;\n}\n/**\n * Converts radians to degrees.\n */\n\nfunction radToDeg(radians) {\n  return radians * 180 / Math.PI;\n} // adapted from https://gist.github.com/stephanbogner/a5f50548a06bec723dcb0991dcbb0856 by https://twitter.com/st_phan\n\nfunction fibonacciOnSphere(buffer, _ref) {\n  var _ref$radius = _ref.radius,\n      radius = _ref$radius === void 0 ? 1 : _ref$radius;\n  var samples = buffer.length / 3;\n  var offset = 2 / samples;\n  var increment = Math.PI * (3 - 2.2360679775);\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var y = i * offset - 1 + offset / 2;\n    var distance = Math.sqrt(1 - Math.pow(y, 2));\n    var phi = i % samples * increment;\n    var x = Math.cos(phi) * distance;\n    var z = Math.sin(phi) * distance;\n    buffer[i] = x * radius;\n    buffer[i + 1] = y * radius;\n    buffer[i + 2] = z * radius;\n  }\n} // @ts-ignore\n\nfunction vectorEquals(a, b) {\n  var eps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.EPSILON;\n  return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n}\n/**\n * Sorts vectors in lexicographic order, works with both v2 and v3\n *\n *  Use as:\n *  const sorted = arrayOfVectors.sort(lexicographicOrder)\n */\n// https://en.wikipedia.org/wiki/Lexicographic_order\n\nfunction lexicographic(a, b) {\n  if (a.x === b.x) {\n    // do a check to see if points is 3D,\n    // in which case add y eq check and sort by z\n    if (typeof a.z !== \"undefined\") {\n      if (a.y === b.y) {\n        return a.z - b.z;\n      }\n    }\n\n    return a.y - b.y;\n  }\n\n  return a.x - b.x;\n}\n/**\n * Convex Hull\n *\n * Returns an array of 2D Vectors representing the convex hull of a set of 2D Vectors\n */\n\n/**\n * Calculate the convex hull of a set of points\n */\n\nfunction convexHull(_points) {\n  var points = _points.sort(lexicographic); // put p1 and p2 in a list lUpper with p1 as the first point\n\n\n  var lUpper = [points[0], points[1]]; // for i <- 3 to n\n\n  for (var i = 2; i < points.length; i++) {\n    lUpper.push(points[i]); // while lUpper contains more than 2 points and the last three points in lUpper do not make a right turn\n\n    while (lUpper.length > 2 && doThreePointsMakeARight(_toConsumableArray(lUpper.slice(-3)))) {\n      // delete the middle of the last three points from lUpper\n      lUpper.splice(lUpper.length - 2, 1);\n    }\n  } // put pn and pn-1 in a list lLower with pn as the first point\n\n\n  var lLower = [points[points.length - 1], points[points.length - 2]]; // for (i <- n - 2 downto 1)\n\n  for (var _i = points.length - 3; _i >= 0; _i--) {\n    // append pi to lLower\n    lLower.push(points[_i]); // while lLower contains more than 2 points and the last three points in lLower do not make a right turn\n\n    while (lLower.length > 2 && doThreePointsMakeARight(_toConsumableArray(lLower.slice(-3)))) {\n      // delete the middle of the last three points from lLower\n      lLower.splice(lLower.length - 2, 1);\n    }\n  } // remove the first and last point from lLower to avoid duplication of the points where the upper and lower hull meet\n\n\n  lLower.splice(0, 1);\n  lLower.splice(lLower.length - 1, 1); // prettier-ignore\n\n  var c = [].concat(lUpper, lLower);\n  return c;\n}\nfunction remap(x, _ref2, _ref3) {\n  var _ref4 = _slicedToArray(_ref2, 2),\n      low1 = _ref4[0],\n      high1 = _ref4[1];\n\n  var _ref5 = _slicedToArray(_ref3, 2),\n      low2 = _ref5[0],\n      high2 = _ref5[1];\n\n  return low2 + (x - low1) * (high2 - low2) / (high1 - low1);\n}\n/**\n *\n * https://www.desmos.com/calculator/vsnmlaljdu\n *\n * Ease-in-out, goes to -Infinite before 0 and Infinite after 1\n *\n * @param t\n * @returns\n */\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\n/**\n *\n * Returns the result of linearly interpolating between input A and input B by input T.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction lerp(v0, v1, t) {\n  return v0 * (1 - t) + v1 * t;\n}\n/**\n *\n * Returns the linear parameter that produces the interpolant specified by input T within the range of input A to input B.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction inverseLerp(v0, v1, t) {\n  return (t - v0) / (v1 - v0);\n}\n/**\n *\n */\n\nfunction normalize(x, y, z) {\n  var m = Math.sqrt(x * x + y * y + z * z);\n  return [x / m, y / m, z / m];\n}\n/**\n *\n */\n\nfunction pointOnCubeToPointOnSphere(x, y, z) {\n  var x2 = x * x;\n  var y2 = y * y;\n  var z2 = z * z;\n  var nx = x * Math.sqrt(1 - (y2 + z2) / 2 + y2 * z2 / 3);\n  var ny = y * Math.sqrt(1 - (z2 + x2) / 2 + z2 * x2 / 3);\n  var nz = z * Math.sqrt(1 - (x2 + y2) / 2 + x2 * y2 / 3);\n  return [nx, ny, nz];\n} // https://math.stackexchange.com/questions/180418/calculate-rotation-matrix-to-align-vector-a-to-vector-b-in-3d\n\n/**\n * Give two unit vectors a and b, returns the transformation matrix that rotates a onto b.\n *\n * */\n\nfunction rotateVectorOnVector(a, b) {\n  var v = new Vector3().crossVectors(a, b);\n  var c = a.dot(b);\n  var i = new Matrix3().identity(); //  skew-symmetric cross-product matrix of 𝑣 https://en.wikipedia.org/wiki/Skew-symmetric_matrix\n  // prettier-ignore\n\n  var vx = new Matrix3().set(0, -v.z, v.y, v.z, 0, -v.x, -v.y, v.x, 0);\n  var vxsquared = new Matrix3().multiplyMatrices(vx, vx).multiplyScalar(1 / (1 + c));\n\n  var _final = matrixSum3(matrixSum3(i, vx), vxsquared);\n\n  return _final;\n} // calculate latitude and longitude (in radians) from point on unit sphere\n\nfunction pointToCoordinate(x, y, z) {\n  var lat = Math.asin(y);\n  var lon = Math.atan2(x, -z);\n  return [lat, lon];\n} // calculate point on unit sphere given latitude and logitude in radians\n\nfunction coordinateToPoint(lat, lon) {\n  var y = Math.sin(lat);\n  var r = Math.cos(lat);\n  var x = Math.sin(lon) * r;\n  var z = -Math.cos(lon) * r;\n  return [x, y, z];\n}\n/**\n * Given a plane and a segment, return the intersection point if it exists or null it doesn't.\n */\n\nfunction planeSegmentIntersection(plane, segment) {\n  var _segment = _slicedToArray(segment, 2),\n      a = _segment[0],\n      b = _segment[1];\n\n  var matrix = rotateVectorOnVector(plane.normal, new Vector3(0, 1, 0));\n  var t = inverseLerp(a.clone().applyMatrix3(matrix).y, b.clone().applyMatrix3(matrix).y, 0);\n  return new Vector3().lerpVectors(a, b, t);\n}\n/**\n * Given a plane and a point, return the distance.\n */\n\nfunction pointToPlaneDistance(p, plane) {\n  var d = plane.normal.dot(p); // TODO\n\n  return d;\n}\nfunction getIndexFrom3D(coords, sides) {\n  var _coords = _slicedToArray(coords, 3),\n      ix = _coords[0],\n      iy = _coords[1],\n      iz = _coords[2];\n\n  var _sides = _slicedToArray(sides, 2),\n      rx = _sides[0],\n      ry = _sides[1];\n\n  return iz * rx * ry + iy * rx + ix;\n}\nfunction get3DFromIndex(index, size) {\n  var _size = _slicedToArray(size, 2),\n      rx = _size[0],\n      ry = _size[1];\n\n  var a = rx * ry;\n  var z = index / a;\n  var b = index - a * z;\n  var y = b / rx;\n  var x = b % rx;\n  return [x, y, z];\n}\nfunction getIndexFrom2D(coords, size) {\n  return coords[0] + size[0] * coords[1];\n}\nfunction get2DFromIndex(index, columns) {\n  var x = index % columns;\n  var y = Math.floor(index / columns);\n  return [x, y];\n}\n\nvar misc = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  clamp: clamp,\n  deltaAngle: deltaAngle,\n  degToRad: degToRad,\n  radToDeg: radToDeg,\n  fibonacciOnSphere: fibonacciOnSphere,\n  vectorEquals: vectorEquals,\n  lexicographic: lexicographic,\n  convexHull: convexHull,\n  remap: remap,\n  fade: fade,\n  lerp: lerp,\n  inverseLerp: inverseLerp,\n  normalize: normalize,\n  pointOnCubeToPointOnSphere: pointOnCubeToPointOnSphere,\n  rotateVectorOnVector: rotateVectorOnVector,\n  pointToCoordinate: pointToCoordinate,\n  coordinateToPoint: coordinateToPoint,\n  planeSegmentIntersection: planeSegmentIntersection,\n  pointToPlaneDistance: pointToPlaneDistance,\n  getIndexFrom3D: getIndexFrom3D,\n  get3DFromIndex: get3DFromIndex,\n  getIndexFrom2D: getIndexFrom2D,\n  get2DFromIndex: get2DFromIndex\n});\n\nexport { degToRad as a, fibonacciOnSphere as b, clamp as c, deltaAngle as d, lexicographic as e, fade as f, convexHull as g, remap as h, inverseLerp as i, rotateVectorOnVector as j, pointToCoordinate as k, lerp as l, misc as m, normalize as n, coordinateToPoint as o, pointOnCubeToPointOnSphere as p, planeSegmentIntersection as q, radToDeg as r, pointToPlaneDistance as s, getIndexFrom3D as t, get3DFromIndex as u, vectorEquals as v, getIndexFrom2D as w, get2DFromIndex as x };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA;;CAEC,GACD,SAAS,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG;IAC5B,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK;AACrC,EAAE,sFAAsF;AAExF,SAAS,OAAO,CAAC,EAAE,MAAM;IACvB,OAAO,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,QAAQ,GAAG;AACvD,EAAE,+DAA+D;AAGjE,SAAS,WAAW,OAAO,EAAE,MAAM;IACjC,IAAI,QAAQ,OAAO,SAAS,SAAS,KAAK,EAAE,GAAG;IAC/C,IAAI,QAAQ,KAAK,EAAE,EAAE,SAAS,KAAK,EAAE,GAAG;IACxC,OAAO;AACT;AACA;;CAEC,GAED,SAAS,SAAS,OAAO;IACvB,OAAO,UAAU,MAAM,KAAK,EAAE;AAChC;AACA;;CAEC,GAED,SAAS,SAAS,OAAO;IACvB,OAAO,UAAU,MAAM,KAAK,EAAE;AAChC,EAAE,qHAAqH;AAEvH,SAAS,kBAAkB,MAAM,EAAE,IAAI;IACrC,IAAI,cAAc,KAAK,MAAM,EACzB,SAAS,gBAAgB,KAAK,IAAI,IAAI;IAC1C,IAAI,UAAU,OAAO,MAAM,GAAG;IAC9B,IAAI,SAAS,IAAI;IACjB,IAAI,YAAY,KAAK,EAAE,GAAG,CAAC,IAAI,YAAY;IAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,IAAI,IAAI,IAAI,SAAS,IAAI,SAAS;QAClC,IAAI,WAAW,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG;QACzC,IAAI,MAAM,IAAI,UAAU;QACxB,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO;QACxB,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO;QACxB,MAAM,CAAC,EAAE,GAAG,IAAI;QAChB,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI;QACpB,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI;IACtB;AACF,EAAE,aAAa;AAEf,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,OAAO,OAAO;IAC5F,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI;AACzF;AACA;;;;;CAKC,GACD,oDAAoD;AAEpD,SAAS,cAAc,CAAC,EAAE,CAAC;IACzB,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;QACf,qCAAqC;QACrC,6CAA6C;QAC7C,IAAI,OAAO,EAAE,CAAC,KAAK,aAAa;YAC9B,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;gBACf,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC;YAClB;QACF;QAEA,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC;IAClB;IAEA,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC;AAClB;AACA;;;;CAIC,GAED;;CAEC,GAED,SAAS,WAAW,OAAO;IACzB,IAAI,SAAS,QAAQ,IAAI,CAAC,gBAAgB,4DAA4D;IAGtG,IAAI,SAAS;QAAC,MAAM,CAAC,EAAE;QAAE,MAAM,CAAC,EAAE;KAAC,EAAE,kBAAkB;IAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,wGAAwG;QAEhI,MAAO,OAAO,MAAM,GAAG,KAAK,CAAA,GAAA,kNAAA,CAAA,IAAuB,AAAD,EAAE,CAAA,GAAA,kNAAA,CAAA,IAAkB,AAAD,EAAE,OAAO,KAAK,CAAC,CAAC,KAAM;YACzF,yDAAyD;YACzD,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,GAAG;QACnC;IACF,EAAE,8DAA8D;IAGhE,IAAI,SAAS;QAAC,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAAE,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;KAAC,EAAE,4BAA4B;IAEjG,IAAK,IAAI,KAAK,OAAO,MAAM,GAAG,GAAG,MAAM,GAAG,KAAM;QAC9C,sBAAsB;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,wGAAwG;QAEjI,MAAO,OAAO,MAAM,GAAG,KAAK,CAAA,GAAA,kNAAA,CAAA,IAAuB,AAAD,EAAE,CAAA,GAAA,kNAAA,CAAA,IAAkB,AAAD,EAAE,OAAO,KAAK,CAAC,CAAC,KAAM;YACzF,yDAAyD;YACzD,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,GAAG;QACnC;IACF,EAAE,qHAAqH;IAGvH,OAAO,MAAM,CAAC,GAAG;IACjB,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,GAAG,IAAI,kBAAkB;IAEvD,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ;IAC1B,OAAO;AACT;AACA,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK;IAC5B,IAAI,QAAQ,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,OAAO,IAC9B,OAAO,KAAK,CAAC,EAAE,EACf,QAAQ,KAAK,CAAC,EAAE;IAEpB,IAAI,QAAQ,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,OAAO,IAC9B,OAAO,KAAK,CAAC,EAAE,EACf,QAAQ,KAAK,CAAC,EAAE;IAEpB,OAAO,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI;AAC3D;AACA;;;;;;;;CAQC,GAED,SAAS,KAAK,CAAC;IACb,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE;AAC3C;AACA;;;;;;;;CAQC,GAED,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC;IACrB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK;AAC7B;AACA;;;;;;;;CAQC,GAED,SAAS,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5B,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;AAC5B;AACA;;CAEC,GAED,SAAS,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACxB,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;IACtC,OAAO;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;KAAE;AAC9B;AACA;;CAEC,GAED,SAAS,2BAA2B,CAAC,EAAE,CAAC,EAAE,CAAC;IACzC,IAAI,KAAK,IAAI;IACb,IAAI,KAAK,IAAI;IACb,IAAI,KAAK,IAAI;IACb,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,KAAK,KAAK;IACrD,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,KAAK,KAAK;IACrD,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,KAAK,KAAK;IACrD,OAAO;QAAC;QAAI;QAAI;KAAG;AACrB,EAAE,gHAAgH;AAElH;;;GAGG,GAEH,SAAS,qBAAqB,CAAC,EAAE,CAAC;IAChC,IAAI,IAAI,IAAI,+IAAA,CAAA,UAAO,GAAG,YAAY,CAAC,GAAG;IACtC,IAAI,IAAI,EAAE,GAAG,CAAC;IACd,IAAI,IAAI,IAAI,+IAAA,CAAA,UAAO,GAAG,QAAQ,IAAI,iGAAiG;IACnI,kBAAkB;IAElB,IAAI,KAAK,IAAI,+IAAA,CAAA,UAAO,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;IAClE,IAAI,YAAY,IAAI,+IAAA,CAAA,UAAO,GAAG,gBAAgB,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;IAEhF,IAAI,SAAS,CAAA,GAAA,gNAAA,CAAA,IAAU,AAAD,EAAE,CAAA,GAAA,gNAAA,CAAA,IAAU,AAAD,EAAE,GAAG,KAAK;IAE3C,OAAO;AACT,EAAE,0EAA0E;AAE5E,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC;IAChC,IAAI,MAAM,KAAK,IAAI,CAAC;IACpB,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC;IACzB,OAAO;QAAC;QAAK;KAAI;AACnB,EAAE,wEAAwE;AAE1E,SAAS,kBAAkB,GAAG,EAAE,GAAG;IACjC,IAAI,IAAI,KAAK,GAAG,CAAC;IACjB,IAAI,IAAI,KAAK,GAAG,CAAC;IACjB,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO;IACxB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO;IACzB,OAAO;QAAC;QAAG;QAAG;KAAE;AAClB;AACA;;CAEC,GAED,SAAS,yBAAyB,KAAK,EAAE,OAAO;IAC9C,IAAI,WAAW,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,SAAS,IACnC,IAAI,QAAQ,CAAC,EAAE,EACf,IAAI,QAAQ,CAAC,EAAE;IAEnB,IAAI,SAAS,qBAAqB,MAAM,MAAM,EAAE,IAAI,+IAAA,CAAA,UAAO,CAAC,GAAG,GAAG;IAClE,IAAI,IAAI,YAAY,EAAE,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE;IACxF,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,WAAW,CAAC,GAAG,GAAG;AACzC;AACA;;CAEC,GAED,SAAS,qBAAqB,CAAC,EAAE,KAAK;IACpC,IAAI,IAAI,MAAM,MAAM,CAAC,GAAG,CAAC,IAAI,OAAO;IAEpC,OAAO;AACT;AACA,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,IAAI,UAAU,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,QAAQ,IACjC,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,OAAO,CAAC,EAAE;IAEnB,IAAI,SAAS,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,OAAO,IAC/B,KAAK,MAAM,CAAC,EAAE,EACd,KAAK,MAAM,CAAC,EAAE;IAElB,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC;AACA,SAAS,eAAe,KAAK,EAAE,IAAI;IACjC,IAAI,QAAQ,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,MAAM,IAC7B,KAAK,KAAK,CAAC,EAAE,EACb,KAAK,KAAK,CAAC,EAAE;IAEjB,IAAI,IAAI,KAAK;IACb,IAAI,IAAI,QAAQ;IAChB,IAAI,IAAI,QAAQ,IAAI;IACpB,IAAI,IAAI,IAAI;IACZ,IAAI,IAAI,IAAI;IACZ,OAAO;QAAC;QAAG;QAAG;KAAE;AAClB;AACA,SAAS,eAAe,MAAM,EAAE,IAAI;IAClC,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;AACxC;AACA,SAAS,eAAe,KAAK,EAAE,OAAO;IACpC,IAAI,IAAI,QAAQ;IAChB,IAAI,IAAI,KAAK,KAAK,CAAC,QAAQ;IAC3B,OAAO;QAAC;QAAG;KAAE;AACf;AAEA,IAAI,OAAO,WAAW,GAAE,OAAO,MAAM,CAAC;IACpC,WAAW;IACX,OAAO;IACP,YAAY;IACZ,UAAU;IACV,UAAU;IACV,mBAAmB;IACnB,cAAc;IACd,eAAe;IACf,YAAY;IACZ,OAAO;IACP,MAAM;IACN,MAAM;IACN,aAAa;IACb,WAAW;IACX,4BAA4B;IAC5B,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,0BAA0B;IAC1B,sBAAsB;IACtB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/vector2-d2bf51f1.esm.js"], "sourcesContent": ["/**\n *\n */\nfunction zero() {\n  return [0, 0];\n}\nfunction one() {\n  return [1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]));\n}\n\nvar vector2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, length as f, distance as g, lengthSqr as l, one as o, sub as s, vector2 as v, zero as z };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;AACD,SAAS;IACP,OAAO;QAAC;QAAG;KAAE;AACf;AACA,SAAS;IACP,OAAO;QAAC;QAAG;KAAE;AACf;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACf,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;KAAC;AACnC;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;KAAE;AAC7B;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACf,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;KAAC;AACnC;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;KAAE;AAC7B;AACA,SAAS,MAAM,CAAC,EAAE,CAAC;IACjB,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;KAAE;AAC7B;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACf,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAClC;AACA;;;CAGC,GAED,SAAS,UAAU,CAAC;IAClB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAClC;AACA;;;CAGC,GAED,SAAS,OAAO,CAAC;IACf,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAC5C;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E;AAEA,IAAI,UAAU,WAAW,GAAE,OAAO,MAAM,CAAC;IACvC,WAAW;IACX,MAAM;IACN,KAAK;IACL,KAAK;IACL,UAAU;IACV,KAAK;IACL,UAAU;IACV,OAAO;IACP,KAAK;IACL,WAAW;IACX,QAAQ;IACR,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/vector3-0a088b7f.esm.js"], "sourcesContent": ["/**\n *\n */\nfunction zero() {\n  return [0, 0, 0];\n}\nfunction one() {\n  return [1, 1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n, a[2] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1], a[2] - b[2]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n, a[2] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n, a[2] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cross(a, b) {\n  var x = a[1] * b[2] - a[2] * b[1];\n  var y = a[2] * b[0] - a[0] * b[2];\n  var z = a[0] * b[1] - a[1] * b[0];\n  return [x, y, z];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1] + a[2] * a[2];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]) + (a[2] - b[2]) * (a[2] - b[2]));\n}\n\nvar vector3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  cross: cross,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, cross as f, length as g, distance as h, lengthSqr as l, one as o, sub as s, vector3 as v, zero as z };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;AACD,SAAS;IACP,OAAO;QAAC;QAAG;QAAG;KAAE;AAClB;AACA,SAAS;IACP,OAAO;QAAC;QAAG;QAAG;KAAE;AAClB;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACf,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;KAAC;AAChD;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;KAAE;AACvC;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACf,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;KAAC;AAChD;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;KAAE;AACvC;AACA,SAAS,MAAM,CAAC,EAAE,CAAC;IACjB,OAAO;QAAC,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;QAAG,CAAC,CAAC,EAAE,GAAG;KAAE;AACvC;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACf,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAChD;AACA,SAAS,MAAM,CAAC,EAAE,CAAC;IACjB,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACjC,OAAO;QAAC;QAAG;QAAG;KAAE;AAClB;AACA;;;CAGC,GAED,SAAS,UAAU,CAAC;IAClB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAChD;AACA;;;CAGC,GAED,SAAS,OAAO,CAAC;IACf,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAC1D;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/G;AAEA,IAAI,UAAU,WAAW,GAAE,OAAO,MAAM,CAAC;IACvC,WAAW;IACX,MAAM;IACN,KAAK;IACL,KAAK;IACL,UAAU;IACV,KAAK;IACL,UAAU;IACV,OAAO;IACP,KAAK;IACL,OAAO;IACP,WAAW;IACX,QAAQ;IACR,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/buffer-d2a4726c.esm.js"], "sourcesContent": ["import { _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Quaternion, Vector3 } from 'three';\nimport { l as lerp$1 } from './misc-7d870b3c.esm.js';\nimport { z as zero, a as add$1 } from './vector2-d2bf51f1.esm.js';\nimport { a as add } from './vector3-0a088b7f.esm.js';\n\nfunction swizzle(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var swizzle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"xyz\";\n  var o = {\n    x: 0,\n    y: 0,\n    z: 0\n  };\n\n  for (var _i = 0; _i < buffer.length; _i += stride) {\n    o.x = buffer[_i];\n    o.y = buffer[_i + 1];\n    o.z = buffer[_i + 2];\n\n    var _swizzle$split = swizzle.split(\"\"),\n        _swizzle$split2 = _slicedToArray(_swizzle$split, 3),\n        x = _swizzle$split2[0],\n        y = _swizzle$split2[1],\n        z = _swizzle$split2[2]; // TODO Fix this ugly type\n\n\n    buffer[_i] = o[x];\n    buffer[_i + 1] = o[y];\n\n    if (stride === 3) {\n      buffer[_i + 2] = o[z];\n    }\n  }\n\n  return buffer;\n}\n/**\n * @param buffer A stride 2 points buffer\n * @param valueGenerator A function that returns the value of the z axis at index i\n * @returns\n */\n\nfunction addAxis(buffer, size) {\n  var valueGenerator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return Math.random();\n  };\n  var newSize = size + 1;\n  var newBuffer = new Float32Array(buffer.length / size * newSize);\n\n  for (var _i2 = 0; _i2 < buffer.length; _i2 += size) {\n    var _j = _i2 / size * newSize;\n\n    newBuffer[_j] = buffer[_i2];\n    newBuffer[_j + 1] = buffer[_i2 + 1];\n\n    if (size === 2) {\n      newBuffer[_j + 2] = valueGenerator(_j);\n    }\n\n    if (size === 3) {\n      newBuffer[_j + 2] = buffer[_i2 + 2];\n      newBuffer[_j + 3] = valueGenerator(_j);\n    }\n  }\n\n  return newBuffer;\n}\n/**\n * Lerps bufferA and bufferB into final\n *\n * @param bufferA\n * @param bufferB\n * @param final\n * @param t\n */\n\nfunction lerp(bufferA, bufferB, _final, t) {\n  for (var _i3 = 0; _i3 < bufferA.length; _i3++) {\n    _final[_i3] = lerp$1(bufferA[_i3], bufferB[_i3], t);\n  }\n} // TODO add stride\n// TODO Fix types & vectors\n\n/**\n *\n * Translate all points in the passed buffer by the passed translactionVector.\n *\n * @param buffer\n * @param translationVector\n * @returns\n */\n\nfunction translate(buffer, translationVector) {\n  var stride = translationVector.length;\n\n  for (var _i4 = 0; _i4 < buffer.length; _i4 += stride) {\n    buffer[_i4] += translationVector[0];\n    buffer[_i4 + 1] += translationVector[1];\n    buffer[_i4 + 2] += translationVector[2];\n  }\n\n  return buffer;\n} // TODO add stride\n// TODO remove quaternion & vector3 dependencies\n\nfunction rotate(buffer, rotation) {\n  var defaultRotation = {\n    center: [0, 0, 0],\n    q: new Quaternion().identity()\n  };\n  var v = new Vector3();\n\n  var _defaultRotation$rota = _objectSpread2(_objectSpread2({}, defaultRotation), rotation),\n      q = _defaultRotation$rota.q,\n      center = _defaultRotation$rota.center;\n\n  for (var _i5 = 0; _i5 < buffer.length; _i5 += 3) {\n    v.set(buffer[_i5] - center[0], buffer[_i5 + 1] - center[1], buffer[_i5 + 2] - center[2]);\n    v.applyQuaternion(q);\n    buffer[_i5] = v.x + center[0];\n    buffer[_i5 + 1] = v.y + center[1];\n    buffer[_i5 + 2] = v.z + center[1];\n  }\n\n  return buffer;\n}\nfunction map(buffer, stride, callback) {\n  for (var _i6 = 0, _j2 = 0; _i6 < buffer.length; _i6 += stride, _j2++) {\n    if (stride === 3) {\n      var res = callback([buffer[_i6], buffer[_i6 + 1], buffer[_i6 + 2]], _j2);\n      buffer.set(res, _i6);\n    } else {\n      buffer.set(callback([buffer[_i6], buffer[_i6 + 1]], _j2), _i6);\n    }\n  }\n\n  return buffer;\n}\n/**\n * Reduces passed buffer\n */\n\nfunction reduce(b, stride, callback, acc) {\n  for (var _i7 = 0, _j3 = 0; _i7 < b.length; _i7 += stride, _j3++) {\n    if (stride === 2) {\n      acc = callback(acc, [b[_i7], b[_i7 + 1]], _j3);\n    } else {\n      acc = callback(acc, [b[_i7], b[_i7 + 1], b[_i7 + 2]], _j3);\n    }\n  }\n\n  return acc;\n}\nfunction expand(b, stride, opts) {\n  var defaultExpandOptions = {\n    center: [0, 0, 0]\n  };\n\n  var _defaultExpandOptions = _objectSpread2(_objectSpread2({}, defaultExpandOptions), opts),\n      center = _defaultExpandOptions.center,\n      distance = _defaultExpandOptions.distance;\n\n  for (var _i8 = 0; _i8 < b.length; _i8 += stride) {\n    /**\n     * 1. translate to origin (subtract the scaling center)\n     * 2. scale by the correct amount (multiply by a constant)\n     * 2. translate from origin (add the scaling center)\n     */\n    b[_i8] = (b[_i8] - center[0]) * (1 + distance) + center[0];\n    b[_i8 + 1] = (b[_i8 + 1] - center[1]) * (1 + distance) + center[1];\n\n    if (stride === 3) {\n      b[_i8 + 2] = (b[_i8 + 2] - center[1]) * (1 + distance) + center[2];\n    }\n  }\n\n  return b;\n}\nfunction center(myBuffer, stride) {\n  return reduce(myBuffer, stride, function (acc, point) {\n    if (stride === 3) {\n      // some type hacking is necessary to avoid type errors going from [n, n] => [n, n, n]\n      // but it's not an actual problem, as this path would always get a v3\n      acc = add(acc, point);\n    } else {\n      acc = add$1(acc, point);\n    }\n\n    return acc;\n  }, zero());\n}\nfunction sort(myBuffer, stride, callback) {\n  // 1. make an array of the correct size\n  var indices = Int16Array.from({\n    length: myBuffer.length / stride\n  }, function (_, i) {\n    return i;\n  }); // 2. sort the indices array\n\n  indices.sort(function (a, b) {\n    var pa = myBuffer.slice(a * stride, a * stride + stride);\n    var pb = myBuffer.slice(b * stride, b * stride + stride);\n    return callback(pa, pb);\n  }); // 3. make a copy of the original array to fetch indices from\n\n  var prevBuffer = myBuffer.slice(0); // 4. mutate the passed array\n\n  for (var _i9 = 0; _i9 < indices.length; _i9++) {\n    var _j4 = indices[_i9];\n    myBuffer.set(prevBuffer.slice(_j4 * stride, _j4 * stride + stride), _i9 * 3);\n  }\n\n  return myBuffer;\n}\n\nvar buffer = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  swizzle: swizzle,\n  addAxis: addAxis,\n  lerp: lerp,\n  translate: translate,\n  rotate: rotate,\n  map: map,\n  reduce: reduce,\n  expand: expand,\n  center: center,\n  sort: sort\n});\n\nexport { addAxis as a, buffer as b, reduce as c, center as d, expand as e, sort as f, lerp as l, map as m, rotate as r, swizzle as s, translate as t };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,SAAS,QAAQ,MAAM;IACrB,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACjF,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAClF,IAAI,IAAI;QACN,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,IAAK,IAAI,KAAK,GAAG,KAAK,OAAO,MAAM,EAAE,MAAM,OAAQ;QACjD,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QAChB,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE;QACpB,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE;QAEpB,IAAI,iBAAiB,QAAQ,KAAK,CAAC,KAC/B,kBAAkB,CAAA,GAAA,kNAAA,CAAA,IAAc,AAAD,EAAE,gBAAgB,IACjD,IAAI,eAAe,CAAC,EAAE,EACtB,IAAI,eAAe,CAAC,EAAE,EACtB,IAAI,eAAe,CAAC,EAAE,EAAE,0BAA0B;QAGtD,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;QACjB,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAErB,IAAI,WAAW,GAAG;YAChB,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QACvB;IACF;IAEA,OAAO;AACT;AACA;;;;CAIC,GAED,SAAS,QAAQ,MAAM,EAAE,IAAI;IAC3B,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QACvF,OAAO,KAAK,MAAM;IACpB;IACA,IAAI,UAAU,OAAO;IACrB,IAAI,YAAY,IAAI,aAAa,OAAO,MAAM,GAAG,OAAO;IAExD,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,MAAM,EAAE,OAAO,KAAM;QAClD,IAAI,KAAK,MAAM,OAAO;QAEtB,SAAS,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI;QAC3B,SAAS,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE;QAEnC,IAAI,SAAS,GAAG;YACd,SAAS,CAAC,KAAK,EAAE,GAAG,eAAe;QACrC;QAEA,IAAI,SAAS,GAAG;YACd,SAAS,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE;YACnC,SAAS,CAAC,KAAK,EAAE,GAAG,eAAe;QACrC;IACF;IAEA,OAAO;AACT;AACA;;;;;;;CAOC,GAED,SAAS,KAAK,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACvC,IAAK,IAAI,MAAM,GAAG,MAAM,QAAQ,MAAM,EAAE,MAAO;QAC7C,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,8MAAA,CAAA,IAAM,AAAD,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE;IACnD;AACF,EAAE,kBAAkB;AACpB,2BAA2B;AAE3B;;;;;;;CAOC,GAED,SAAS,UAAU,MAAM,EAAE,iBAAiB;IAC1C,IAAI,SAAS,kBAAkB,MAAM;IAErC,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,MAAM,EAAE,OAAO,OAAQ;QACpD,MAAM,CAAC,IAAI,IAAI,iBAAiB,CAAC,EAAE;QACnC,MAAM,CAAC,MAAM,EAAE,IAAI,iBAAiB,CAAC,EAAE;QACvC,MAAM,CAAC,MAAM,EAAE,IAAI,iBAAiB,CAAC,EAAE;IACzC;IAEA,OAAO;AACT,EAAE,kBAAkB;AACpB,gDAAgD;AAEhD,SAAS,OAAO,MAAM,EAAE,QAAQ;IAC9B,IAAI,kBAAkB;QACpB,QAAQ;YAAC;YAAG;YAAG;SAAE;QACjB,GAAG,IAAI,+IAAA,CAAA,aAAU,GAAG,QAAQ;IAC9B;IACA,IAAI,IAAI,IAAI,+IAAA,CAAA,UAAO;IAEnB,IAAI,wBAAwB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,kBAAkB,WAC5E,IAAI,sBAAsB,CAAC,EAC3B,SAAS,sBAAsB,MAAM;IAEzC,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,MAAM,EAAE,OAAO,EAAG;QAC/C,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE;QACvF,EAAE,eAAe,CAAC;QAClB,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE;QAC7B,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE;QACjC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE;IACnC;IAEA,OAAO;AACT;AACA,SAAS,IAAI,MAAM,EAAE,MAAM,EAAE,QAAQ;IACnC,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,OAAO,MAAM,EAAE,OAAO,QAAQ,MAAO;QACpE,IAAI,WAAW,GAAG;YAChB,IAAI,MAAM,SAAS;gBAAC,MAAM,CAAC,IAAI;gBAAE,MAAM,CAAC,MAAM,EAAE;gBAAE,MAAM,CAAC,MAAM,EAAE;aAAC,EAAE;YACpE,OAAO,GAAG,CAAC,KAAK;QAClB,OAAO;YACL,OAAO,GAAG,CAAC,SAAS;gBAAC,MAAM,CAAC,IAAI;gBAAE,MAAM,CAAC,MAAM,EAAE;aAAC,EAAE,MAAM;QAC5D;IACF;IAEA,OAAO;AACT;AACA;;CAEC,GAED,SAAS,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IACtC,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,OAAO,QAAQ,MAAO;QAC/D,IAAI,WAAW,GAAG;YAChB,MAAM,SAAS,KAAK;gBAAC,CAAC,CAAC,IAAI;gBAAE,CAAC,CAAC,MAAM,EAAE;aAAC,EAAE;QAC5C,OAAO;YACL,MAAM,SAAS,KAAK;gBAAC,CAAC,CAAC,IAAI;gBAAE,CAAC,CAAC,MAAM,EAAE;gBAAE,CAAC,CAAC,MAAM,EAAE;aAAC,EAAE;QACxD;IACF;IAEA,OAAO;AACT;AACA,SAAS,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI;IAC7B,IAAI,uBAAuB;QACzB,QAAQ;YAAC;YAAG;YAAG;SAAE;IACnB;IAEA,IAAI,wBAAwB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,uBAAuB,OACjF,SAAS,sBAAsB,MAAM,EACrC,WAAW,sBAAsB,QAAQ;IAE7C,IAAK,IAAI,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,OAAO,OAAQ;QAC/C;;;;KAIC,GACD,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,QAAQ,IAAI,MAAM,CAAC,EAAE;QAC1D,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,QAAQ,IAAI,MAAM,CAAC,EAAE;QAElE,IAAI,WAAW,GAAG;YAChB,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,QAAQ,IAAI,MAAM,CAAC,EAAE;QACpE;IACF;IAEA,OAAO;AACT;AACA,SAAS,OAAO,QAAQ,EAAE,MAAM;IAC9B,OAAO,OAAO,UAAU,QAAQ,SAAU,GAAG,EAAE,KAAK;QAClD,IAAI,WAAW,GAAG;YAChB,qFAAqF;YACrF,qEAAqE;YACrE,MAAM,CAAA,GAAA,iNAAA,CAAA,IAAG,AAAD,EAAE,KAAK;QACjB,OAAO;YACL,MAAM,CAAA,GAAA,iNAAA,CAAA,IAAK,AAAD,EAAE,KAAK;QACnB;QAEA,OAAO;IACT,GAAG,CAAA,GAAA,iNAAA,CAAA,IAAI,AAAD;AACR;AACA,SAAS,KAAK,QAAQ,EAAE,MAAM,EAAE,QAAQ;IACtC,uCAAuC;IACvC,IAAI,UAAU,WAAW,IAAI,CAAC;QAC5B,QAAQ,SAAS,MAAM,GAAG;IAC5B,GAAG,SAAU,CAAC,EAAE,CAAC;QACf,OAAO;IACT,IAAI,4BAA4B;IAEhC,QAAQ,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QACzB,IAAI,KAAK,SAAS,KAAK,CAAC,IAAI,QAAQ,IAAI,SAAS;QACjD,IAAI,KAAK,SAAS,KAAK,CAAC,IAAI,QAAQ,IAAI,SAAS;QACjD,OAAO,SAAS,IAAI;IACtB,IAAI,6DAA6D;IAEjE,IAAI,aAAa,SAAS,KAAK,CAAC,IAAI,6BAA6B;IAEjE,IAAK,IAAI,MAAM,GAAG,MAAM,QAAQ,MAAM,EAAE,MAAO;QAC7C,IAAI,MAAM,OAAO,CAAC,IAAI;QACtB,SAAS,GAAG,CAAC,WAAW,KAAK,CAAC,MAAM,QAAQ,MAAM,SAAS,SAAS,MAAM;IAC5E;IAEA,OAAO;AACT;AAEA,IAAI,SAAS,WAAW,GAAE,OAAO,MAAM,CAAC;IACtC,WAAW;IACX,SAAS;IACT,SAAS;IACT,MAAM;IACN,WAAW;IACX,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/classCallCheck-9098b006.esm.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nexport { _classCallCheck as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,QAAQ,EAAE,WAAW;IAC5C,IAAI,CAAC,CAAC,oBAAoB,WAAW,GAAG;QACtC,MAAM,IAAI,UAAU;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/index-43782085.esm.js"], "sourcesContent": ["import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-7d870b3c.esm.js';\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (<EMAIL>).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * <PERSON>. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n\n  _classCallCheck(this, Grad);\n\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\n\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n\n  _seed = Math.floor(_seed);\n\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n\n  for (var i = 0; i < 256; i++) {\n    var v;\n\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y),\n      Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\n\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\n\nvar TAU = Math.PI * 2; // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n\n  if (seed === 0) {\n    seed = 311;\n  }\n\n  return seed;\n}\n\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\n\nvar Generator = function Generator(_seed) {\n  var _this = this;\n\n  _classCallCheck(this, Generator);\n\n  _defineProperty(this, \"seed\", 0);\n\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere.radius,\n      center = _defaultSphere$sphere.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere2.radius,\n      center = _defaultSphere$sphere2.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle.radius,\n      center = _defaultCircle$circle.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle2.radius,\n      center = _defaultCircle$circle2.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n      sides = _defaultRect$rect.sides,\n      center = _defaultRect$rect.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box.sides,\n      center = _defaultBox$box.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box2.sides,\n      center = _defaultBox$box2.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\n\nexport { Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA;;;;;;;;;;;;;;CAcC,GAED,IAAI,OAAO,SAAS,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,IAAI,QAAQ,IAAI;IAEhB,CAAA,GAAA,wNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE;IAEtB,CAAA,GAAA,uNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE,QAAQ,SAAU,CAAC,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG;IACjC;IAEA,CAAA,GAAA,uNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE,QAAQ,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG;IAC/C;IAEA,IAAI,CAAC,CAAC,GAAG;IACT,IAAI,CAAC,CAAC,GAAG;IACT,IAAI,CAAC,CAAC,GAAG;AACX;AAEA,IAAI,QAAQ;IAAC,IAAI,KAAK,GAAG,GAAG;IAAI,IAAI,KAAK,CAAC,GAAG,GAAG;IAAI,IAAI,KAAK,GAAG,CAAC,GAAG;IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG;IAAI,IAAI,KAAK,GAAG,GAAG;IAAI,IAAI,KAAK,CAAC,GAAG,GAAG;IAAI,IAAI,KAAK,GAAG,GAAG,CAAC;IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC;IAAI,IAAI,KAAK,GAAG,GAAG;IAAI,IAAI,KAAK,GAAG,CAAC,GAAG;IAAI,IAAI,KAAK,GAAG,GAAG,CAAC;IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC;CAAG;AAC5P,IAAI,IAAI;IAAC;IAAK;IAAK;IAAK;IAAI;IAAI;IAAI;IAAK;IAAI;IAAK;IAAI;IAAI;IAAI;IAAK;IAAK;IAAG;IAAK;IAAK;IAAI;IAAK;IAAI;IAAI;IAAK;IAAG;IAAI;IAAI;IAAK;IAAI;IAAI;IAAI;IAAK;IAAG;IAAK;IAAK;IAAK;IAAK;IAAI;IAAG;IAAI;IAAK;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAI;IAAI;IAAI;IAAI;IAAK;IAAI;IAAI;IAAK;IAAK;IAAI;IAAI;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAI;IAAK;IAAI;IAAK;IAAK;IAAI;IAAI;IAAK;IAAI;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAI;IAAI;IAAI;IAAK;IAAI;IAAK;IAAK;IAAK;IAAI;IAAI;IAAI;IAAI;IAAK;IAAG;IAAK;IAAI;IAAI;IAAK;IAAI;IAAK;IAAK;IAAK;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAG;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAG;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAI;IAAI;IAAK;IAAK;IAAK;IAAI;IAAK;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAG;IAAI;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAG;IAAK;IAAI;IAAI;IAAK;IAAI;IAAI;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAI;IAAI;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAI;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAK;IAAK;IAAI;IAAI;IAAK;IAAG;IAAK;IAAK;IAAK;IAAK;IAAK;IAAI;IAAK;IAAK;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAI;IAAI;IAAK;IAAI;IAAK;CAAI,EAAE,6EAA6E;AAEzuC,IAAI,OAAO,IAAI,MAAM;AACrB,IAAI,QAAQ,IAAI,MAAM,MAAM,6EAA6E;AACzG,wEAAwE;AAExE,IAAI,OAAO,SAAS,KAAK,KAAK;IAC5B,IAAI,QAAQ,KAAK,QAAQ,GAAG;QAC1B,qBAAqB;QACrB,SAAS;IACX;IAEA,QAAQ,KAAK,KAAK,CAAC;IAEnB,IAAI,QAAQ,KAAK;QACf,SAAS,SAAS;IACpB;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,IAAI;QAEJ,IAAI,IAAI,GAAG;YACT,IAAI,CAAC,CAAC,EAAE,GAAG,QAAQ;QACrB,OAAO;YACL,IAAI,CAAC,CAAC,EAAE,GAAG,SAAS,IAAI;QAC1B;QAEA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG;IAC3C;AACF;AACA,KAAK;AACL;;;;GAIG,GACH,2DAA2D;AAE3D,IAAI,KAAK,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC;AAChC,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,IAAI;AAC9B,IAAI,KAAK,IAAI;AACb,IAAI,KAAK,IAAI,GAAG,mBAAmB;AAEnC,IAAI,WAAW,SAAS,SAAS,GAAG,EAAE,GAAG;IACvC,IAAI,IAAI,IAAI,IAAI,6CAA6C;IAC7D,gEAAgE;IAEhE,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,sBAAsB;IAEhD,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;IACzB,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;IACzB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;IAClB,IAAI,KAAK,MAAM,IAAI,GAAG,oDAAoD;IAE1E,IAAI,KAAK,MAAM,IAAI,GAAG,iEAAiE;IACvF,qCAAqC;IAErC,IAAI,IAAI,IAAI,gEAAgE;IAE5E,IAAI,KAAK,IAAI;QACX,gDAAgD;QAChD,KAAK;QACL,KAAK;IACP,OAAO;QACL,gDAAgD;QAChD,KAAK;QACL,KAAK;IACP,EAAE,kEAAkE;IACpE,oEAAoE;IACpE,oBAAoB;IAGpB,IAAI,KAAK,KAAK,KAAK,IAAI,qDAAqD;IAE5E,IAAI,KAAK,KAAK,KAAK;IACnB,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,mDAAmD;IAE7E,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,oEAAoE;IAE9F,KAAK;IACL,KAAK;IACL,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;IAC5B,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC;IACtC,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,oDAAoD;IAE1F,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;IAE9B,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,sCAAsC;IACzE;IAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;IAE9B,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;IAC9B;IAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;IAE9B,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;IAC9B,EAAE,mEAAmE;IACrE,gEAAgE;IAGhE,OAAO,KAAK,CAAC,KAAK,KAAK,EAAE;AAC3B,GAAG,mBAAmB;AAEtB,IAAI,WAAW,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG;IAC5C,IAAI,IAAI,IAAI,IAAI,IAAI,4CAA4C;IAChE,gEAAgE;IAEhE,IAAI,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI,sBAAsB;IAEtD,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;IACzB,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;IACzB,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;IACzB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;IACtB,IAAI,KAAK,MAAM,IAAI,GAAG,oDAAoD;IAE1E,IAAI,KAAK,MAAM,IAAI;IACnB,IAAI,KAAK,MAAM,IAAI,GAAG,0EAA0E;IAChG,qCAAqC;IAErC,IAAI,IAAI,IAAI,IAAI,yDAAyD;IAEzE,IAAI,IAAI,IAAI,IAAI,wDAAwD;IAExE,IAAI,MAAM,IAAI;QACZ,IAAI,MAAM,IAAI;YACZ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,OAAO,IAAI,MAAM,IAAI;YACnB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,OAAO;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;IACF,OAAO;QACL,IAAI,KAAK,IAAI;YACX,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,OAAO,IAAI,KAAK,IAAI;YAClB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,OAAO;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;IACF,EAAE,uEAAuE;IACzE,2EAA2E;IAC3E,6EAA6E;IAC7E,WAAW;IAGX,IAAI,KAAK,KAAK,KAAK,IAAI,4BAA4B;IAEnD,IAAI,KAAK,KAAK,KAAK;IACnB,IAAI,KAAK,KAAK,KAAK;IACnB,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,2BAA2B;IAEtD,IAAI,KAAK,KAAK,KAAK,IAAI;IACvB,IAAI,KAAK,KAAK,KAAK,IAAI;IACvB,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,4BAA4B;IAEtD,IAAI,KAAK,KAAK,IAAI,IAAI;IACtB,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,mEAAmE;IAE7F,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IACtC,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IACrD,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IACrD,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,mDAAmD;IAEvG,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;IAExC,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,sCAAsC;IAC7E;IAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;IAExC,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI;IAClC;IAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;IAExC,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI;IAClC;IAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;IAExC,IAAI,KAAK,GAAG;QACV,KAAK;IACP,OAAO;QACL,MAAM;QACN,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI;IAClC,EAAE,mEAAmE;IACrE,gEAAgE;IAGhE,OAAO,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;AAChC,GAAG,2BAA2B;AAC9B,kBAAkB;AAElB,IAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,CAAC;IACjC,uCAAuC;IACvC,IAAI,IAAI,KAAK,KAAK,CAAC,IACf,IAAI,KAAK,KAAK,CAAC,IAAI,wDAAwD;IAE/E,IAAI,IAAI;IACR,IAAI,IAAI,GAAG,gFAAgF;IAE3F,IAAI,IAAI;IACR,IAAI,IAAI,KAAK,8DAA8D;IAE3E,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG;IACrC,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI;IAC7C,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG;IAC7C,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,qCAAqC;IAE9F,IAAI,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,IAAI,+BAA+B;IAEhD,OAAO,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,KAAK,KAAK,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,KAAK,KAAK,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE;AACzD,GAAG,kBAAkB;AAErB,IAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;IACpC,uCAAuC;IACvC,IAAI,IAAI,KAAK,KAAK,CAAC,IACf,IAAI,KAAK,KAAK,CAAC,IACf,IAAI,KAAK,KAAK,CAAC,IAAI,yDAAyD;IAEhF,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI,GAAG,gFAAgF;IAE3F,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,IAAI,KAAK,+DAA+D;IAE5E,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG;IACnD,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI;IAC3D,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG;IAC3D,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACnE,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG;IAC3D,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI;IACnE,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG;IACnE,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,2CAA2C;IAE1H,IAAI,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE;IACb,IAAI,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE;IACb,IAAI,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,IAAI,cAAc;IAE/B,OAAO,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,MAAM,MAAM,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,MAAM,MAAM,IAAI,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,MAAM,MAAM,IAAI,CAAA,GAAA,8MAAA,CAAA,IAAI,AAAD,EAAE,MAAM,MAAM,IAAI,IAAI;AACpH;AAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;IACrC,WAAW;IACX,MAAM;IACN,UAAU;IACV,UAAU;IACV,SAAS;IACT,SAAS;AACX;AAEA,IAAI,MAAM,KAAK,EAAE,GAAG,GAAG,4GAA4G;AAEnI,SAAS,cAAc,IAAI;IACzB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,KAAK,GAAG,CAAC;IAClB,OAAO,IAAI,OAAO,SAAS,UAAU;QACnC,IAAI,SAAS;QACb,OAAO;QAEP,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,UAAU,CAAC,KAAK,EAAE,CAAC,IAAI;QAC1D;IACF;IAEA,IAAI,SAAS,GAAG;QACd,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,QAAQ,cAAc;IAC1B,OAAO;QACL,IAAI,SAAS,QAAQ,QAAQ;QAC7B,QAAQ;QACR,OAAO,SAAS;IAClB;AACF;AAEA,IAAI,YAAY,SAAS,UAAU,KAAK;IACtC,IAAI,QAAQ,IAAI;IAEhB,CAAA,GAAA,wNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE;IAEtB,CAAA,GAAA,uNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE,QAAQ;IAE9B,CAAA,GAAA,uNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE,QAAQ,SAAU,IAAI;QAC1C,MAAM,IAAI,GAAG;QACb,MAAM,KAAK,GAAG,UAAU;IAC1B;IAEA,CAAA,GAAA,uNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE,SAAS,UAAU,IAAI,CAAC,IAAI;IAElD,IAAI,CAAC,IAAI,CAAC;AACZ;AACA,IAAI,aAAa,IAAI,UAAU,KAAK,MAAM;AAC1C;;CAEC,GAED,IAAI,gBAAgB;IAClB,QAAQ;IACR,QAAQ;QAAC;QAAG;QAAG;KAAE;AACnB,GAAG,8BAA8B;AACjC,kEAAkE;AAClE,0DAA0D;AAE1D,SAAS,SAAS,MAAM,EAAE,MAAM;IAC9B,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,wBAAwB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,gBAAgB,SAC1E,SAAS,sBAAsB,MAAM,EACrC,SAAS,sBAAsB,MAAM;IAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,IAAI,IAAI,IAAI,KAAK;QACjB,IAAI,IAAI,IAAI,KAAK;QACjB,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI;QAC9B,IAAI,MAAM,MAAM;QAChB,MAAM,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,OAAO,SAAS,MAAM,CAAC,EAAE;QAChE,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,OAAO,SAAS,MAAM,CAAC,EAAE;QACpE,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,SAAS,MAAM,CAAC,EAAE;IACtD;IAEA,OAAO;AACT,EAAE,8EAA8E;AAEhF,SAAS,SAAS,MAAM,EAAE,MAAM;IAC9B,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,yBAAyB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,gBAAgB,SAC3E,SAAS,uBAAuB,MAAM,EACtC,SAAS,uBAAuB,MAAM;IAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI;QAClC,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI;QAC1B,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI;QAC1B,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI;QAC1B,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;QACxC,IAAI,IAAI,IAAI;QACZ,IAAI,IAAI,IAAI;QACZ,IAAI,IAAI,IAAI;QACZ,MAAM,CAAC,EAAE,GAAG,IAAI,SAAS,MAAM,CAAC,EAAE;QAClC,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,SAAS,MAAM,CAAC,EAAE;QACtC,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,SAAS,MAAM,CAAC,EAAE;IACxC;IAEA,OAAO;AACT;AACA;;CAEC,GAED,IAAI,gBAAgB;IAClB,QAAQ;IACR,QAAQ;QAAC;QAAG;KAAE;AAChB,GAAG,qDAAqD;AAExD,SAAS,SAAS,MAAM,EAAE,MAAM;IAC9B,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,wBAAwB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,gBAAgB,SAC1E,SAAS,sBAAsB,MAAM,EACrC,SAAS,sBAAsB,MAAM;IAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,IAAI,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,KAAK;QACpC,IAAI,QAAQ,IAAI,KAAK,KAAK;QAC1B,MAAM,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE;QAC3C,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE;IACjD;IAEA,OAAO;AACT;AACA,SAAS,SAAS,MAAM,EAAE,MAAM;IAC9B,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,yBAAyB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,gBAAgB,SAC3E,SAAS,uBAAuB,MAAM,EACtC,SAAS,uBAAuB,MAAM;IAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,IAAI,QAAQ,IAAI,KAAK,KAAK;QAC1B,MAAM,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,SAAS,MAAM,CAAC,EAAE;QAChD,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,SAAS,MAAM,CAAC,EAAE;IACtD;IAEA,OAAO;AACT;AACA;;CAEC,GAED,IAAI,cAAc;IAChB,OAAO;IACP,QAAQ;QAAC;QAAG;KAAE;AAChB;AACA,SAAS,OAAO,MAAM,EAAE,IAAI;IAC1B,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,oBAAoB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,cAAc,OACpE,QAAQ,kBAAkB,KAAK,EAC/B,SAAS,kBAAkB,MAAM;IAErC,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IACxD,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IAExD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;QACnD,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;IACzD;IAEA,OAAO;AACT;AACA,SAAS,OAAO,MAAM,EAAE,IAAI;IAC1B,OAAO;AACT;AACA;;CAEC,GAED,SAAS,MAAM,MAAM,EAAE,GAAG;IACxB,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,kBAAkB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,aAAa,MACjE,QAAQ,gBAAgB,KAAK,EAC7B,SAAS,gBAAgB,MAAM;IAEnC,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IACxD,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IACxD,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IAExD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;QACnD,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;QACvD,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;IACzD;IAEA,OAAO;AACT;AACA,IAAI,aAAa;IACf,OAAO;IACP,QAAQ;QAAC;QAAG;QAAG;KAAE;AACnB;AACA,SAAS,MAAM,MAAM,EAAE,GAAG;IACxB,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAE9E,IAAI,mBAAmB,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAA,GAAA,uNAAA,CAAA,IAAc,AAAD,EAAE,CAAC,GAAG,aAAa,MAClE,QAAQ,iBAAiB,KAAK,EAC9B,SAAS,iBAAiB,MAAM;IAEpC,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IACxD,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IACxD,IAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,KAAK,CAAC,EAAE;IAExD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;QACnD,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;QACvD,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;IACzD;IAEA,OAAO;AACT;AAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;IACrC,WAAW;IACX,WAAW;IACX,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/easing-3be59c6d.esm.js"], "sourcesContent": ["import { a as _toConsumableArray } from './triangle-b62b9067.esm.js';\nimport { Color, Vector3, Quaternion, Vector2, Vector4, Euler, Spherical, Matrix4 } from 'three';\nimport { d as deltaAngle } from './misc-7d870b3c.esm.js';\n\n/**\n * Rounded square wave easing\n */\n\nvar rsqw = function rsqw(t) {\n  var delta = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.01;\n  var a = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var f = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1 / (2 * Math.PI);\n  return a / Math.atan(1 / delta) * Math.atan(Math.sin(2 * Math.PI * t * f) / delta);\n};\n/**\n * Exponential easing\n */\n\nvar exp = function exp(t) {\n  return 1 / (1 + t + 0.48 * t * t + 0.235 * t * t * t);\n};\n/**\n * Damp, based on Game Programming Gems 4 Chapter 1.10\n *   Return value indicates whether the animation is still running.\n */\n\nfunction damp(\n/** The object */\ncurrent,\n/** The key to animate */\nprop,\n/** To goal value */\ntarget) {\n  var smoothTime = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.25;\n  var delta = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.01;\n  var maxSpeed = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : Infinity;\n  var easing = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : exp;\n  var eps = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 0.001;\n  var vel = \"velocity_\" + prop;\n  if (current.__damp === undefined) current.__damp = {};\n  if (current.__damp[vel] === undefined) current.__damp[vel] = 0;\n\n  if (Math.abs(current[prop] - target) <= eps) {\n    current[prop] = target;\n    return false;\n  }\n\n  smoothTime = Math.max(0.0001, smoothTime);\n  var omega = 2 / smoothTime;\n  var t = easing(omega * delta);\n  var change = current[prop] - target;\n  var originalTo = target; // Clamp maximum maxSpeed\n\n  var maxChange = maxSpeed * smoothTime;\n  change = Math.min(Math.max(change, -maxChange), maxChange);\n  target = current[prop] - change;\n  var temp = (current.__damp[vel] + omega * change) * delta;\n  current.__damp[vel] = (current.__damp[vel] - omega * temp) * t;\n  var output = target + (change + temp) * t; // Prevent overshooting\n\n  if (originalTo - current[prop] > 0.0 === output > originalTo) {\n    output = originalTo;\n    current.__damp[vel] = (output - originalTo) / delta;\n  }\n\n  current[prop] = output;\n  return true;\n}\n/**\n * DampAngle, based on Game Programming Gems 4 Chapter 1.10\n */\n\nfunction dampAngle(current, prop, target, smoothTime, delta, maxSpeed, easing, eps) {\n  return damp(current, prop, current[prop] + deltaAngle(current[prop], target), smoothTime, delta, maxSpeed, easing, eps);\n}\n/**\n * Vector2D Damp\n */\n\nvar v2d = /*@__PURE__*/new Vector2();\nvar a2, b2;\nfunction damp2(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v2d.setScalar(target);else if (Array.isArray(target)) v2d.set(target[0], target[1]);else v2d.copy(target);\n  a2 = damp(current, \"x\", v2d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b2 = damp(current, \"y\", v2d.y, smoothTime, delta, maxSpeed, easing, eps);\n  return a2 || b2;\n}\n/**\n * Vector3D Damp\n */\n\nvar v3d = /*@__PURE__*/new Vector3();\nvar a3, b3, c3;\nfunction damp3(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v3d.setScalar(target);else if (Array.isArray(target)) v3d.set(target[0], target[1], target[2]);else v3d.copy(target);\n  a3 = damp(current, \"x\", v3d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b3 = damp(current, \"y\", v3d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c3 = damp(current, \"z\", v3d.z, smoothTime, delta, maxSpeed, easing, eps);\n  return a3 || b3 || c3;\n}\n/**\n * Vector4D Damp\n */\n\nvar v4d = /*@__PURE__*/new Vector4();\nvar a4, b4, c4, d4;\nfunction damp4(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v4d.setScalar(target);else if (Array.isArray(target)) v4d.set(target[0], target[1], target[2], target[3]);else v4d.copy(target);\n  a4 = damp(current, \"x\", v4d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b4 = damp(current, \"y\", v4d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c4 = damp(current, \"z\", v4d.z, smoothTime, delta, maxSpeed, easing, eps);\n  d4 = damp(current, \"w\", v4d.w, smoothTime, delta, maxSpeed, easing, eps);\n  return a4 || b4 || c4 || d4;\n}\n/**\n * Euler Damp\n */\n\nvar rot = /*@__PURE__*/new Euler();\nvar aE, bE, cE;\nfunction dampE(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) rot.set(target[0], target[1], target[2], target[3]);else rot.copy(target);\n  aE = dampAngle(current, \"x\", rot.x, smoothTime, delta, maxSpeed, easing, eps);\n  bE = dampAngle(current, \"y\", rot.y, smoothTime, delta, maxSpeed, easing, eps);\n  cE = dampAngle(current, \"z\", rot.z, smoothTime, delta, maxSpeed, easing, eps);\n  return aE || bE || cE;\n}\n/**\n * Color Damp\n */\n\nvar col = /*@__PURE__*/new Color();\nvar aC, bC, cC;\nfunction dampC(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (target instanceof Color) col.copy(target);else if (Array.isArray(target)) col.setRGB(target[0], target[1], target[2]);else col.set(target);\n  aC = damp(current, \"r\", col.r, smoothTime, delta, maxSpeed, easing, eps);\n  bC = damp(current, \"g\", col.g, smoothTime, delta, maxSpeed, easing, eps);\n  cC = damp(current, \"b\", col.b, smoothTime, delta, maxSpeed, easing, eps);\n  return aC || bC || cC;\n}\n/**\n * Quaternion Damp\n * https://gist.github.com/maxattack/4c7b4de00f5c1b95a33b\n * Copyright 2016 Max Kaufmann (<EMAIL>)\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nvar qt = /*@__PURE__*/new Quaternion();\nvar v4result = /*@__PURE__*/new Vector4();\nvar v4velocity = /*@__PURE__*/new Vector4();\nvar v4error = /*@__PURE__*/new Vector4();\nvar aQ, bQ, cQ, dQ;\nfunction dampQ(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n  if (Array.isArray(target)) qt.set(target[0], target[1], target[2], target[3]);else qt.copy(target);\n  var multi = current.dot(qt) > 0 ? 1 : -1;\n  qt.x *= multi;\n  qt.y *= multi;\n  qt.z *= multi;\n  qt.w *= multi;\n  aQ = damp(current, \"x\", qt.x, smoothTime, delta, maxSpeed, easing, eps);\n  bQ = damp(current, \"y\", qt.y, smoothTime, delta, maxSpeed, easing, eps);\n  cQ = damp(current, \"z\", qt.z, smoothTime, delta, maxSpeed, easing, eps);\n  dQ = damp(current, \"w\", qt.w, smoothTime, delta, maxSpeed, easing, eps); // smooth damp (nlerp approx)\n\n  v4result.set(current.x, current.y, current.z, current.w).normalize();\n  v4velocity.set(cur.__damp.velocity_x, cur.__damp.velocity_y, cur.__damp.velocity_z, cur.__damp.velocity_w); // ensure deriv is tangent\n\n  v4error.copy(v4result).multiplyScalar(v4velocity.dot(v4result) / v4result.dot(v4result));\n  cur.__damp.velocity_x -= v4error.x;\n  cur.__damp.velocity_y -= v4error.y;\n  cur.__damp.velocity_z -= v4error.z;\n  cur.__damp.velocity_w -= v4error.w;\n  current.set(v4result.x, v4result.y, v4result.z, v4result.w);\n  return aQ || bQ || cQ || dQ;\n}\n/**\n * Spherical Damp\n */\n\nvar spherical = /*@__PURE__*/new Spherical();\nvar aS, bS, cS;\nfunction dampS(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) spherical.set(target[0], target[1], target[2]);else spherical.copy(target);\n  aS = damp(current, \"radius\", spherical.radius, smoothTime, delta, maxSpeed, easing, eps);\n  bS = dampAngle(current, \"phi\", spherical.phi, smoothTime, delta, maxSpeed, easing, eps);\n  cS = dampAngle(current, \"theta\", spherical.theta, smoothTime, delta, maxSpeed, easing, eps);\n  return aS || bS || cS;\n}\n/**\n * Matrix4 Damp\n */\n\nvar mat = /*@__PURE__*/new Matrix4();\nvar mPos = /*@__PURE__*/new Vector3();\nvar mRot = /*@__PURE__*/new Quaternion();\nvar mSca = /*@__PURE__*/new Vector3();\nvar aM, bM, cM;\nfunction dampM(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n\n  if (cur.__damp === undefined) {\n    cur.__damp = {\n      position: new Vector3(),\n      rotation: new Quaternion(),\n      scale: new Vector3()\n    };\n    current.decompose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  }\n\n  if (Array.isArray(target)) mat.set.apply(mat, _toConsumableArray(target));else mat.copy(target);\n  mat.decompose(mPos, mRot, mSca);\n  aM = damp3(cur.__damp.position, mPos, smoothTime, delta, maxSpeed, easing, eps);\n  bM = dampQ(cur.__damp.rotation, mRot, smoothTime, delta, maxSpeed, easing, eps);\n  cM = damp3(cur.__damp.scale, mSca, smoothTime, delta, maxSpeed, easing, eps);\n  current.compose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  return aM || bM || cM;\n}\n\nvar easing = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  rsqw: rsqw,\n  exp: exp,\n  damp: damp,\n  dampAngle: dampAngle,\n  damp2: damp2,\n  damp3: damp3,\n  damp4: damp4,\n  dampE: dampE,\n  dampC: dampC,\n  dampQ: dampQ,\n  dampS: dampS,\n  dampM: dampM\n});\n\nexport { exp as a, dampAngle as b, damp2 as c, damp as d, easing as e, damp3 as f, damp4 as g, dampE as h, dampC as i, dampQ as j, dampS as k, dampM as l, rsqw as r };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA;;CAEC,GAED,IAAI,OAAO,SAAS,KAAK,CAAC;IACxB,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAChF,IAAI,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAC5E,IAAI,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE;IAC5F,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG,IAAI,KAAK;AAC9E;AACA;;CAEC,GAED,IAAI,MAAM,SAAS,IAAI,CAAC;IACtB,OAAO,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;AACtD;AACA;;;CAGC,GAED,SAAS,KACT,eAAe,GACf,OAAO,EACP,uBAAuB,GACvB,IAAI,EACJ,kBAAkB,GAClB,MAAM;IACJ,IAAI,aAAa,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACrF,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAChF,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACnF,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACjF,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAC9E,IAAI,MAAM,cAAc;IACxB,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,MAAM,GAAG,CAAC;IACpD,IAAI,QAAQ,MAAM,CAAC,IAAI,KAAK,WAAW,QAAQ,MAAM,CAAC,IAAI,GAAG;IAE7D,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,WAAW,KAAK;QAC3C,OAAO,CAAC,KAAK,GAAG;QAChB,OAAO;IACT;IAEA,aAAa,KAAK,GAAG,CAAC,QAAQ;IAC9B,IAAI,QAAQ,IAAI;IAChB,IAAI,IAAI,OAAO,QAAQ;IACvB,IAAI,SAAS,OAAO,CAAC,KAAK,GAAG;IAC7B,IAAI,aAAa,QAAQ,yBAAyB;IAElD,IAAI,YAAY,WAAW;IAC3B,SAAS,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,YAAY;IAChD,SAAS,OAAO,CAAC,KAAK,GAAG;IACzB,IAAI,OAAO,CAAC,QAAQ,MAAM,CAAC,IAAI,GAAG,QAAQ,MAAM,IAAI;IACpD,QAAQ,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI;IAC7D,IAAI,SAAS,SAAS,CAAC,SAAS,IAAI,IAAI,GAAG,uBAAuB;IAElE,IAAI,aAAa,OAAO,CAAC,KAAK,GAAG,QAAQ,SAAS,YAAY;QAC5D,SAAS;QACT,QAAQ,MAAM,CAAC,IAAI,GAAG,CAAC,SAAS,UAAU,IAAI;IAChD;IAEA,OAAO,CAAC,KAAK,GAAG;IAChB,OAAO;AACT;AACA;;CAEC,GAED,SAAS,UAAU,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAChF,OAAO,KAAK,SAAS,MAAM,OAAO,CAAC,KAAK,GAAG,CAAA,GAAA,8MAAA,CAAA,IAAU,AAAD,EAAE,OAAO,CAAC,KAAK,EAAE,SAAS,YAAY,OAAO,UAAU,QAAQ;AACrH;AACA;;CAEC,GAED,IAAI,MAAM,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AAClC,IAAI,IAAI;AACR,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,OAAO,WAAW,UAAU,IAAI,SAAS,CAAC;SAAa,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,IAAI,IAAI,CAAC;IAClI,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,OAAO,MAAM;AACf;AACA;;CAEC,GAED,IAAI,MAAM,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AAClC,IAAI,IAAI,IAAI;AACZ,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,OAAO,WAAW,UAAU,IAAI,SAAS,CAAC;SAAa,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,IAAI,IAAI,CAAC;IAC7I,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,OAAO,MAAM,MAAM;AACrB;AACA;;CAEC,GAED,IAAI,MAAM,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AAClC,IAAI,IAAI,IAAI,IAAI;AAChB,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,OAAO,WAAW,UAAU,IAAI,SAAS,CAAC;SAAa,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,IAAI,IAAI,CAAC;IACxJ,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,OAAO,MAAM,MAAM,MAAM;AAC3B;AACA;;CAEC,GAED,IAAI,MAAM,WAAW,GAAE,IAAI,+IAAA,CAAA,QAAK;AAChC,IAAI,IAAI,IAAI;AACZ,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,IAAI,IAAI,CAAC;IAC7F,KAAK,UAAU,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACzE,KAAK,UAAU,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACzE,KAAK,UAAU,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACzE,OAAO,MAAM,MAAM;AACrB;AACA;;CAEC,GAED,IAAI,MAAM,WAAW,GAAE,IAAI,+IAAA,CAAA,QAAK;AAChC,IAAI,IAAI,IAAI;AACZ,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,kBAAkB,+IAAA,CAAA,QAAK,EAAE,IAAI,IAAI,CAAC;SAAa,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,IAAI,GAAG,CAAC;IACvI,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,KAAK,KAAK,SAAS,KAAK,IAAI,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpE,OAAO,MAAM,MAAM;AACrB;AACA;;;;;;;CAOC,GAED,IAAI,KAAK,WAAW,GAAE,IAAI,+IAAA,CAAA,aAAU;AACpC,IAAI,WAAW,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AACvC,IAAI,aAAa,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AACzC,IAAI,UAAU,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AACtC,IAAI,IAAI,IAAI,IAAI;AAChB,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,MAAM;IACV,IAAI,MAAM,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,GAAG,IAAI,CAAC;IAC3F,IAAI,QAAQ,QAAQ,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC;IACvC,GAAG,CAAC,IAAI;IACR,GAAG,CAAC,IAAI;IACR,GAAG,CAAC,IAAI;IACR,GAAG,CAAC,IAAI;IACR,KAAK,KAAK,SAAS,KAAK,GAAG,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACnE,KAAK,KAAK,SAAS,KAAK,GAAG,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACnE,KAAK,KAAK,SAAS,KAAK,GAAG,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ;IACnE,KAAK,KAAK,SAAS,KAAK,GAAG,CAAC,EAAE,YAAY,OAAO,UAAU,QAAQ,MAAM,6BAA6B;IAEtG,SAAS,GAAG,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;IAClE,WAAW,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,UAAU,GAAG,0BAA0B;IAEtI,QAAQ,IAAI,CAAC,UAAU,cAAc,CAAC,WAAW,GAAG,CAAC,YAAY,SAAS,GAAG,CAAC;IAC9E,IAAI,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC;IAClC,IAAI,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC;IAClC,IAAI,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC;IAClC,IAAI,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC;IAClC,QAAQ,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC;IAC1D,OAAO,MAAM,MAAM,MAAM;AAC3B;AACA;;CAEC,GAED,IAAI,YAAY,WAAW,GAAE,IAAI,+IAAA,CAAA,YAAS;AAC1C,IAAI,IAAI,IAAI;AACZ,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,MAAM,OAAO,CAAC,SAAS,UAAU,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;SAAO,UAAU,IAAI,CAAC;IAC9F,KAAK,KAAK,SAAS,UAAU,UAAU,MAAM,EAAE,YAAY,OAAO,UAAU,QAAQ;IACpF,KAAK,UAAU,SAAS,OAAO,UAAU,GAAG,EAAE,YAAY,OAAO,UAAU,QAAQ;IACnF,KAAK,UAAU,SAAS,SAAS,UAAU,KAAK,EAAE,YAAY,OAAO,UAAU,QAAQ;IACvF,OAAO,MAAM,MAAM;AACrB;AACA;;CAEC,GAED,IAAI,MAAM,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AAClC,IAAI,OAAO,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AACnC,IAAI,OAAO,WAAW,GAAE,IAAI,+IAAA,CAAA,aAAU;AACtC,IAAI,OAAO,WAAW,GAAE,IAAI,+IAAA,CAAA,UAAO;AACnC,IAAI,IAAI,IAAI;AACZ,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACtE,IAAI,MAAM;IAEV,IAAI,IAAI,MAAM,KAAK,WAAW;QAC5B,IAAI,MAAM,GAAG;YACX,UAAU,IAAI,+IAAA,CAAA,UAAO;YACrB,UAAU,IAAI,+IAAA,CAAA,aAAU;YACxB,OAAO,IAAI,+IAAA,CAAA,UAAO;QACpB;QACA,QAAQ,SAAS,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,KAAK;IAC9E;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAA,GAAA,kNAAA,CAAA,IAAkB,AAAD,EAAE;SAAc,IAAI,IAAI,CAAC;IACxF,IAAI,SAAS,CAAC,MAAM,MAAM;IAC1B,KAAK,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,YAAY,OAAO,UAAU,QAAQ;IAC3E,KAAK,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,YAAY,OAAO,UAAU,QAAQ;IAC3E,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,MAAM,YAAY,OAAO,UAAU,QAAQ;IACxE,QAAQ,OAAO,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,KAAK;IAC1E,OAAO,MAAM,MAAM;AACrB;AAEA,IAAI,SAAS,WAAW,GAAE,OAAO,MAAM,CAAC;IACtC,WAAW;IACX,MAAM;IACN,KAAK;IACL,MAAM;IACN,WAAW;IACX,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/geometry-982366ff.esm.js"], "sourcesContent": ["import { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { _ as _setPrototypeOf, a as _isNativeReflectConstruct } from './isNativeReflectConstruct-5594d075.esm.js';\nimport * as THREE from 'three';\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n\n  var _super = _createSuper(RoundedPlaneGeometry);\n\n  function RoundedPlaneGeometry() {\n    var _this;\n\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n\n    _classCallCheck(this, RoundedPlaneGeometry);\n\n    _this = _super.call(this); // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n\n    _this.setIndex(new THREE.BufferAttribute(new Uint32Array(indices), 1));\n\n    _this.setAttribute(\"position\", new THREE.BufferAttribute(new Float32Array(positions), 3));\n\n    _this.setAttribute(\"uv\", new THREE.BufferAttribute(new Float32Array(uvs), 2));\n\n    return _this;\n  }\n\n  return RoundedPlaneGeometry;\n}(THREE.BufferGeometry); // Author: https://stackoverflow.com/users/268905/knee-cola\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\nfunction applySphereUV(bufferGeometry) {\n  var uvs = [];\n  var vertices = [];\n\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    vertices.push(new THREE.Vector3(x, y, z));\n  }\n\n  var polarVertices = vertices.map(cartesian2polar);\n\n  for (var _i = 0; _i < polarVertices.length / 3; _i++) {\n    var tri = new THREE.Triangle(vertices[_i * 3 + 0], vertices[_i * 3 + 1], vertices[_i * 3 + 2]);\n    var normal = tri.getNormal(new THREE.Vector3());\n\n    for (var f = 0; f < 3; f++) {\n      var vertex = polarVertices[_i * 3 + f];\n\n      if (vertex.theta === 0 && (vertex.phi === 0 || vertex.phi === Math.PI)) {\n        var alignedVertice = vertex.phi === 0 ? _i * 3 + 1 : _i * 3 + 0;\n        vertex = {\n          r: vertex.r,\n          phi: vertex.phi,\n          theta: polarVertices[alignedVertice].theta\n        };\n      }\n\n      if (vertex.theta === Math.PI && cartesian2polar(normal).theta < Math.PI / 2) {\n        vertex.theta = -Math.PI;\n      }\n\n      var canvasPoint = polar2canvas(vertex);\n      uvs.push(1 - canvasPoint.x, 1 - canvasPoint.y);\n    }\n  }\n\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new THREE.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction cartesian2polar(position) {\n  var r = Math.sqrt(position.x * position.x + position.z * position.z + position.y * position.y);\n  return {\n    r: r,\n    phi: Math.acos(position.y / r),\n    theta: Math.atan2(position.z, position.x)\n  };\n}\n\nfunction polar2canvas(polarPoint) {\n  return {\n    y: polarPoint.phi / Math.PI,\n    x: (polarPoint.theta + Math.PI) / (2 * Math.PI)\n  };\n} // Author: Alex Khoroshylov (https://stackoverflow.com/users/8742287/alex-khoroshylov)\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\n\nfunction applyBoxUV(bufferGeometry) {\n  bufferGeometry.computeBoundingBox();\n  var bboxSize = bufferGeometry.boundingBox.getSize(new THREE.Vector3());\n  var boxSize = Math.min(bboxSize.x, bboxSize.y, bboxSize.z);\n  var boxGeometry = new THREE.BoxGeometry(boxSize, boxSize, boxSize);\n  var cube = new THREE.Mesh(boxGeometry);\n  cube.rotation.set(0, 0, 0);\n  cube.updateWorldMatrix(true, false);\n  var transformMatrix = cube.matrix.clone().invert();\n  var uvBbox = new THREE.Box3(new THREE.Vector3(-boxSize / 2, -boxSize / 2, -boxSize / 2), new THREE.Vector3(boxSize / 2, boxSize / 2, boxSize / 2));\n\n  _applyBoxUV(bufferGeometry, transformMatrix, uvBbox, boxSize);\n\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction _applyBoxUV(geom, transformMatrix, bbox, bbox_max_size) {\n  var coords = [];\n  coords.length = 2 * geom.attributes.position.array.length / 3; //maps 3 verts of 1 face on the better side of the cube\n  //side of the cube can be XY, XZ or YZ\n\n  var makeUVs = function makeUVs(v0, v1, v2) {\n    //pre-rotate the model so that cube sides match world axis\n    v0.applyMatrix4(transformMatrix);\n    v1.applyMatrix4(transformMatrix);\n    v2.applyMatrix4(transformMatrix); //get normal of the face, to know into which cube side it maps better\n\n    var n = new THREE.Vector3();\n    n.crossVectors(v1.clone().sub(v0), v1.clone().sub(v2)).normalize();\n    n.x = Math.abs(n.x);\n    n.y = Math.abs(n.y);\n    n.z = Math.abs(n.z);\n    var uv0 = new THREE.Vector2();\n    var uv1 = new THREE.Vector2();\n    var uv2 = new THREE.Vector2(); // xz mapping\n\n    if (n.y > n.x && n.y > n.z) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (bbox.max.z - v0.z) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (bbox.max.z - v1.z) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (bbox.max.z - v2.z) / bbox_max_size;\n    } else if (n.x > n.y && n.x > n.z) {\n      uv0.x = (v0.z - bbox.min.z) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.z - bbox.min.z) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.z - bbox.min.z) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    } else if (n.z > n.y && n.z > n.x) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    }\n\n    return {\n      uv0: uv0,\n      uv1: uv1,\n      uv2: uv2\n    };\n  };\n\n  if (geom.index) {\n    // is it indexed buffer geometry?\n    for (var vi = 0; vi < geom.index.array.length; vi += 3) {\n      var idx0 = geom.index.array[vi];\n      var idx1 = geom.index.array[vi + 1];\n      var idx2 = geom.index.array[vi + 2];\n      var vx0 = geom.attributes.position.array[3 * idx0];\n      var vy0 = geom.attributes.position.array[3 * idx0 + 1];\n      var vz0 = geom.attributes.position.array[3 * idx0 + 2];\n      var vx1 = geom.attributes.position.array[3 * idx1];\n      var vy1 = geom.attributes.position.array[3 * idx1 + 1];\n      var vz1 = geom.attributes.position.array[3 * idx1 + 2];\n      var vx2 = geom.attributes.position.array[3 * idx2];\n      var vy2 = geom.attributes.position.array[3 * idx2 + 1];\n      var vz2 = geom.attributes.position.array[3 * idx2 + 2];\n      var v0 = new THREE.Vector3(vx0, vy0, vz0);\n      var v1 = new THREE.Vector3(vx1, vy1, vz1);\n      var v2 = new THREE.Vector3(vx2, vy2, vz2);\n      var uvs = makeUVs(v0, v1, v2);\n      coords[2 * idx0] = uvs.uv0.x;\n      coords[2 * idx0 + 1] = uvs.uv0.y;\n      coords[2 * idx1] = uvs.uv1.x;\n      coords[2 * idx1 + 1] = uvs.uv1.y;\n      coords[2 * idx2] = uvs.uv2.x;\n      coords[2 * idx2 + 1] = uvs.uv2.y;\n    }\n  } else {\n    for (var _vi = 0; _vi < geom.attributes.position.array.length; _vi += 9) {\n      var _vx = geom.attributes.position.array[_vi];\n      var _vy = geom.attributes.position.array[_vi + 1];\n      var _vz = geom.attributes.position.array[_vi + 2];\n      var _vx2 = geom.attributes.position.array[_vi + 3];\n      var _vy2 = geom.attributes.position.array[_vi + 4];\n      var _vz2 = geom.attributes.position.array[_vi + 5];\n      var _vx3 = geom.attributes.position.array[_vi + 6];\n      var _vy3 = geom.attributes.position.array[_vi + 7];\n      var _vz3 = geom.attributes.position.array[_vi + 8];\n\n      var _v = new THREE.Vector3(_vx, _vy, _vz);\n\n      var _v2 = new THREE.Vector3(_vx2, _vy2, _vz2);\n\n      var _v3 = new THREE.Vector3(_vx3, _vy3, _vz3);\n\n      var _uvs = makeUVs(_v, _v2, _v3);\n\n      var _idx = _vi / 3;\n\n      var _idx2 = _idx + 1;\n\n      var _idx3 = _idx + 2;\n\n      coords[2 * _idx] = _uvs.uv0.x;\n      coords[2 * _idx + 1] = _uvs.uv0.y;\n      coords[2 * _idx2] = _uvs.uv1.x;\n      coords[2 * _idx2 + 1] = _uvs.uv1.y;\n      coords[2 * _idx3] = _uvs.uv2.x;\n      coords[2 * _idx3 + 1] = _uvs.uv2.y;\n    }\n  }\n\n  if (geom.attributes.uv) delete geom.attributes.uv;\n  geom.setAttribute(\"uv\", new THREE.Float32BufferAttribute(coords, 2));\n}\n\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry,\n  applySphereUV: applySphereUV,\n  applyBoxUV: applyBoxUV\n});\n\nexport { RoundedPlaneGeometry as R, applySphereUV as a, applyBoxUV as b, geometry as g };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,SAAS,UAAU,QAAQ,EAAE,UAAU;IACrC,IAAI,OAAO,eAAe,cAAc,eAAe,MAAM;QAC3D,MAAM,IAAI,UAAU;IACtB;IAEA,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,cAAc,WAAW,SAAS,EAAE;QACrE,aAAa;YACX,OAAO;YACP,UAAU;YACV,cAAc;QAChB;IACF;IACA,IAAI,YAAY,CAAA,GAAA,kOAAA,CAAA,IAAe,AAAD,EAAE,UAAU;AAC5C;AAEA,SAAS,gBAAgB,CAAC;IACxB,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,GAAG,SAAS,gBAAgB,CAAC;QAC1F,OAAO,EAAE,SAAS,IAAI,OAAO,cAAc,CAAC;IAC9C;IACA,OAAO,gBAAgB;AACzB;AAEA,SAAS,uBAAuB,IAAI;IAClC,IAAI,SAAS,KAAK,GAAG;QACnB,MAAM,IAAI,eAAe;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,2BAA2B,IAAI,EAAE,IAAI;IAC5C,IAAI,QAAQ,CAAC,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU,GAAG;QACpE,OAAO;IACT,OAAO,IAAI,SAAS,KAAK,GAAG;QAC1B,MAAM,IAAI,UAAU;IACtB;IAEA,OAAO,uBAAuB;AAChC;AAEA,SAAS,aAAa,OAAO;IAC3B,IAAI,4BAA4B,CAAA,GAAA,kOAAA,CAAA,IAAyB,AAAD;IACxD,OAAO,SAAS;QACd,IAAI,QAAQ,gBAAgB,UACxB;QAEJ,IAAI,2BAA2B;YAC7B,IAAI,YAAY,gBAAgB,IAAI,EAAE,WAAW;YACjD,SAAS,QAAQ,SAAS,CAAC,OAAO,WAAW;QAC/C,OAAO;YACL,SAAS,MAAM,KAAK,CAAC,IAAI,EAAE;QAC7B;QAEA,OAAO,2BAA2B,IAAI,EAAE;IAC1C;AACF;AAEA,IAAI,uBAAuB,WAAW,GAAE,SAAU,qBAAqB;IACrE,UAAU,sBAAsB;IAEhC,IAAI,SAAS,aAAa;IAE1B,SAAS;QACP,IAAI;QAEJ,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAChF,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QACjF,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QACjF,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAEnF,CAAA,GAAA,wNAAA,CAAA,IAAe,AAAD,EAAE,IAAI,EAAE;QAEtB,QAAQ,OAAO,IAAI,CAAC,IAAI,GAAG,iBAAiB;QAE5C,IAAI,KAAK,QAAQ,IAAI,QAAQ,cAAc;QAE3C,IAAI,KAAK,SAAS,IAAI,QAAQ,eAAe;QAE7C,IAAI,KAAK,SAAS,OAAO,SAAS;QAElC,IAAI,KAAK,CAAC,QAAQ,MAAM,IAAI,OAAO,UAAU;QAE7C,IAAI,KAAK,SAAS,QAAQ,QAAQ;QAElC,IAAI,KAAK,CAAC,SAAS,MAAM,IAAI,QAAQ,SAAS;QAE9C,IAAI,YAAY;YAAC;YAAI;YAAI;YAAG,CAAC;YAAI;YAAI;YAAG,CAAC;YAAI,CAAC;YAAI;YAAG;YAAI,CAAC;YAAI;SAAE;QAChE,IAAI,MAAM;YAAC;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QAC1C,IAAI,IAAI;YAAC,IAAI,CAAC,WAAW,CAAC,IAAI;YAAG,IAAI,CAAC,WAAW,CAAC,IAAI;YAAG,WAAW;YAAG,WAAW;YAAG,IAAI,CAAC,WAAW,CAAC,IAAI;YAAG;YAAG;YAAG,IAAI,CAAC,WAAW,CAAC,IAAI;YAAG;YAAG,IAAI,CAAC,WAAW,CAAC,IAAI;YAAG;YAAG;SAAE;QAC3K,IAAI,UAAU;YAAC,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,GAAG;YAAE,CAAC,CAAC,EAAE;YAAE,CAAC,CAAC,GAAG;YAAE,CAAC,CAAC,GAAG;SAAC;QAC7H,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC;YAC5B,KAAK,IAAI,IAAI,KAAK,CAAC;YACnB,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK;YAC3B,KAAK,IAAI,IAAI,KAAK;YAElB,IAAK,IAAI,IAAI,GAAG,KAAK,UAAU,IAAK;gBAClC,MAAM,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,QAAQ;gBACrC,MAAM,KAAK,GAAG,CAAC;gBACf,MAAM,KAAK,GAAG,CAAC;gBACf,UAAU,IAAI,CAAC,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK;gBACrD,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK;gBAElC,IAAI,IAAI,UAAU;oBAChB,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,IAAI;oBAC/B,QAAQ,IAAI,CAAC,GAAG,KAAK,MAAM;gBAC7B;YACF;QACF;QAEA,MAAM,QAAQ,CAAC,IAAI,+IAAA,CAAA,kBAAqB,CAAC,IAAI,YAAY,UAAU;QAEnE,MAAM,YAAY,CAAC,YAAY,IAAI,+IAAA,CAAA,kBAAqB,CAAC,IAAI,aAAa,YAAY;QAEtF,MAAM,YAAY,CAAC,MAAM,IAAI,+IAAA,CAAA,kBAAqB,CAAC,IAAI,aAAa,MAAM;QAE1E,OAAO;IACT;IAEA,OAAO;AACT,EAAE,+IAAA,CAAA,iBAAoB,GAAG,2DAA2D;AACpF,+EAA+E;AAE/E,SAAS,cAAc,cAAc;IACnC,IAAI,MAAM,EAAE;IACZ,IAAI,WAAW,EAAE;IAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,IAAK;QAC5E,IAAI,IAAI,eAAe,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;QAC3D,IAAI,IAAI,eAAe,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;QAC3D,IAAI,IAAI,eAAe,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;QAC3D,SAAS,IAAI,CAAC,IAAI,+IAAA,CAAA,UAAa,CAAC,GAAG,GAAG;IACxC;IAEA,IAAI,gBAAgB,SAAS,GAAG,CAAC;IAEjC,IAAK,IAAI,KAAK,GAAG,KAAK,cAAc,MAAM,GAAG,GAAG,KAAM;QACpD,IAAI,MAAM,IAAI,+IAAA,CAAA,WAAc,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE;QAC7F,IAAI,SAAS,IAAI,SAAS,CAAC,IAAI,+IAAA,CAAA,UAAa;QAE5C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAI,SAAS,aAAa,CAAC,KAAK,IAAI,EAAE;YAEtC,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,KAAK,EAAE,GAAG;gBACtE,IAAI,iBAAiB,OAAO,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;gBAC9D,SAAS;oBACP,GAAG,OAAO,CAAC;oBACX,KAAK,OAAO,GAAG;oBACf,OAAO,aAAa,CAAC,eAAe,CAAC,KAAK;gBAC5C;YACF;YAEA,IAAI,OAAO,KAAK,KAAK,KAAK,EAAE,IAAI,gBAAgB,QAAQ,KAAK,GAAG,KAAK,EAAE,GAAG,GAAG;gBAC3E,OAAO,KAAK,GAAG,CAAC,KAAK,EAAE;YACzB;YAEA,IAAI,cAAc,aAAa;YAC/B,IAAI,IAAI,CAAC,IAAI,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC;QAC/C;IACF;IAEA,IAAI,eAAe,UAAU,CAAC,EAAE,EAAE,OAAO,eAAe,UAAU,CAAC,EAAE;IACrE,eAAe,YAAY,CAAC,MAAM,IAAI,+IAAA,CAAA,yBAA4B,CAAC,KAAK;IACxE,eAAe,UAAU,CAAC,EAAE,CAAC,WAAW,GAAG;IAC3C,OAAO;AACT;AAEA,SAAS,gBAAgB,QAAQ;IAC/B,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC;IAC7F,OAAO;QACL,GAAG;QACH,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG;QAC5B,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;IAC1C;AACF;AAEA,SAAS,aAAa,UAAU;IAC9B,OAAO;QACL,GAAG,WAAW,GAAG,GAAG,KAAK,EAAE;QAC3B,GAAG,CAAC,WAAW,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE;IAChD;AACF,EAAE,sFAAsF;AACxF,+EAA+E;AAG/E,SAAS,WAAW,cAAc;IAChC,eAAe,kBAAkB;IACjC,IAAI,WAAW,eAAe,WAAW,CAAC,OAAO,CAAC,IAAI,+IAAA,CAAA,UAAa;IACnE,IAAI,UAAU,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC;IACzD,IAAI,cAAc,IAAI,+IAAA,CAAA,cAAiB,CAAC,SAAS,SAAS;IAC1D,IAAI,OAAO,IAAI,+IAAA,CAAA,OAAU,CAAC;IAC1B,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG;IACxB,KAAK,iBAAiB,CAAC,MAAM;IAC7B,IAAI,kBAAkB,KAAK,MAAM,CAAC,KAAK,GAAG,MAAM;IAChD,IAAI,SAAS,IAAI,+IAAA,CAAA,OAAU,CAAC,IAAI,+IAAA,CAAA,UAAa,CAAC,CAAC,UAAU,GAAG,CAAC,UAAU,GAAG,CAAC,UAAU,IAAI,IAAI,+IAAA,CAAA,UAAa,CAAC,UAAU,GAAG,UAAU,GAAG,UAAU;IAE/I,YAAY,gBAAgB,iBAAiB,QAAQ;IAErD,eAAe,UAAU,CAAC,EAAE,CAAC,WAAW,GAAG;IAC3C,OAAO;AACT;AAEA,SAAS,YAAY,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa;IAC7D,IAAI,SAAS,EAAE;IACf,OAAO,MAAM,GAAG,IAAI,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,uDAAuD;IACtH,sCAAsC;IAEtC,IAAI,UAAU,SAAS,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;QACvC,0DAA0D;QAC1D,GAAG,YAAY,CAAC;QAChB,GAAG,YAAY,CAAC;QAChB,GAAG,YAAY,CAAC,kBAAkB,qEAAqE;QAEvG,IAAI,IAAI,IAAI,+IAAA,CAAA,UAAa;QACzB,EAAE,YAAY,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,SAAS;QAChE,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC;QAClB,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC;QAClB,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC;QAClB,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAa;QAC3B,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAa;QAC3B,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAa,IAAI,aAAa;QAE5C,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;YAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI;QAChC,OAAO,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;YACjC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;QAChC,OAAO,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;YACjC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;QAChC;QAEA,OAAO;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;IACF;IAEA,IAAI,KAAK,KAAK,EAAE;QACd,iCAAiC;QACjC,IAAK,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,EAAG;YACtD,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG;YAC/B,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;YACnC,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;YACnC,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK;YAClD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;YACtD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;YACtD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK;YAClD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;YACtD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;YACtD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK;YAClD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;YACtD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE;YACtD,IAAI,KAAK,IAAI,+IAAA,CAAA,UAAa,CAAC,KAAK,KAAK;YACrC,IAAI,KAAK,IAAI,+IAAA,CAAA,UAAa,CAAC,KAAK,KAAK;YACrC,IAAI,KAAK,IAAI,+IAAA,CAAA,UAAa,CAAC,KAAK,KAAK;YACrC,IAAI,MAAM,QAAQ,IAAI,IAAI;YAC1B,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;YAC5B,MAAM,CAAC,IAAI,OAAO,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;YAC5B,MAAM,CAAC,IAAI,OAAO,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;YAC5B,MAAM,CAAC,IAAI,OAAO,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC;QAClC;IACF,OAAO;QACL,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAG;YACvE,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI;YAC7C,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YACjD,IAAI,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YACjD,IAAI,OAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,OAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,OAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,OAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,OAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,OAAO,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE;YAElD,IAAI,KAAK,IAAI,+IAAA,CAAA,UAAa,CAAC,KAAK,KAAK;YAErC,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAa,CAAC,MAAM,MAAM;YAExC,IAAI,MAAM,IAAI,+IAAA,CAAA,UAAa,CAAC,MAAM,MAAM;YAExC,IAAI,OAAO,QAAQ,IAAI,KAAK;YAE5B,IAAI,OAAO,MAAM;YAEjB,IAAI,QAAQ,OAAO;YAEnB,IAAI,QAAQ,OAAO;YAEnB,MAAM,CAAC,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,OAAO,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,QAAQ,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,QAAQ,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;QACpC;IACF;IAEA,IAAI,KAAK,UAAU,CAAC,EAAE,EAAE,OAAO,KAAK,UAAU,CAAC,EAAE;IACjD,KAAK,YAAY,CAAC,MAAM,IAAI,+IAAA,CAAA,yBAA4B,CAAC,QAAQ;AACnE;AAEA,IAAI,WAAW,WAAW,GAAE,OAAO,MAAM,CAAC;IACxC,WAAW;IACX,sBAAsB;IACtB,eAAe;IACf,YAAY;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/three-eb2ad8c0.esm.js"], "sourcesContent": ["import { Vector3, Vector2 } from 'three';\n\n/**\n * Helpers for converting buffers to and from Three.js objects\n */\n\n/**\n * Convents passed buffer of passed stride to an array of vectors with the correct length.\n *\n * @param buffer\n * @param stride\n * @returns\n */\nfunction bufferToVectors(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var p = [];\n\n  for (var i = 0, j = 0; i < buffer.length; i += stride, j++) {\n    if (stride === 3) {\n      p[j] = new Vector3(buffer[i], buffer[i + 1], buffer[i + 2]);\n    } else {\n      p[j] = new Vector2(buffer[i], buffer[i + 1]);\n    }\n  }\n\n  return p;\n}\n/**\n * Transforms a passed Vector2 or Vector3 array to a points buffer\n *\n * @param vectorArray\n * @returns\n */\n\nfunction vectorsToBuffer(vectorArray) {\n  var l = vectorArray.length;\n  var stride = vectorArray[0].hasOwnProperty(\"z\") ? 3 : 2;\n  var buffer = new Float32Array(l * stride);\n\n  for (var i = 0; i < l; i++) {\n    var j = i * stride;\n    buffer[j] = vectorArray[i].x;\n    buffer[j + 1] = vectorArray[i].y;\n\n    if (stride === 3) {\n      buffer[j + 2] = vectorArray[i].z;\n    }\n  }\n\n  return buffer;\n}\n\nvar three = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  bufferToVectors: bufferToVectors,\n  vectorsToBuffer: vectorsToBuffer\n});\n\nexport { bufferToVectors as b, three as t, vectorsToBuffer as v };\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA;;CAEC,GAED;;;;;;CAMC,GACD,SAAS,gBAAgB,MAAM;IAC7B,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACjF,IAAI,IAAI,EAAE;IAEV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,QAAQ,IAAK;QAC1D,IAAI,WAAW,GAAG;YAChB,CAAC,CAAC,EAAE,GAAG,IAAI,+IAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE;QAC5D,OAAO;YACL,CAAC,CAAC,EAAE,GAAG,IAAI,+IAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE;QAC7C;IACF;IAEA,OAAO;AACT;AACA;;;;;CAKC,GAED,SAAS,gBAAgB,WAAW;IAClC,IAAI,IAAI,YAAY,MAAM;IAC1B,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,IAAI;IACtD,IAAI,SAAS,IAAI,aAAa,IAAI;IAElC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,IAAI,IAAI,IAAI;QACZ,MAAM,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QAEhC,IAAI,WAAW,GAAG;YAChB,MAAM,CAAC,IAAI,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QAClC;IACF;IAEA,OAAO;AACT;AAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;IACrC,WAAW;IACX,iBAAiB;IACjB,iBAAiB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/%40react-three/postprocessing/node_modules/maath/dist/maath.esm.js"], "sourcesContent": ["export { b as buffer } from './buffer-d2a4726c.esm.js';\nexport { i as random } from './index-43782085.esm.js';\nexport { e as easing } from './easing-3be59c6d.esm.js';\nexport { g as geometry } from './geometry-982366ff.esm.js';\nexport { m as matrix } from './matrix-baa530bf.esm.js';\nexport { m as misc } from './misc-7d870b3c.esm.js';\nexport { t as three } from './three-eb2ad8c0.esm.js';\nexport { t as triangle } from './triangle-b62b9067.esm.js';\nexport { v as vector2 } from './vector2-d2bf51f1.esm.js';\nexport { v as vector3 } from './vector3-0a088b7f.esm.js';\nimport './objectSpread2-284232a6.esm.js';\nimport 'three';\nimport './classCallCheck-9098b006.esm.js';\nimport './isNativeReflectConstruct-5594d075.esm.js';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA", "ignoreList": [0], "debugId": null}}]}