'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface HiddenNavigationProps {
  secretsFound: string[];
}

export function HiddenNavigation({ secretsFound }: HiddenNavigationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      
      // Show navigation when mouse is in top-left corner
      if (e.clientX < 100 && e.clientY < 100) {
        setIsVisible(true);
      } else if (e.clientX > 200 || e.clientY > 200) {
        setIsVisible(false);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const navigationItems = [
    { id: 'echoes', label: '◦ echoes', unlocked: secretsFound.includes('logo_sequence') },
    { id: 'fragments', label: '◦ fragments', unlocked: secretsFound.includes('konami_code') },
    { id: 'void', label: '◦ the void', unlocked: secretsFound.includes('rapid_clicks') },
    { id: 'contact', label: '◦ contact', unlocked: secretsFound.length >= 3 },
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.nav
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
          className="fixed top-8 left-8 z-40"
        >
          <div className="bg-void-600 border border-whisper-100 p-6 backdrop-blur-sm">
            <h3 className="text-whisper-300 text-sm font-mono mb-4">navigate</h3>
            <ul className="space-y-2">
              {navigationItems.map((item) => (
                <li key={item.id}>
                  <button
                    className={`text-xs font-mono transition-colors duration-300 ${
                      item.unlocked 
                        ? 'text-whisper-400 hover:text-whisper-200 cursor-pointer' 
                        : 'text-whisper-700 cursor-not-allowed'
                    }`}
                    disabled={!item.unlocked}
                  >
                    {item.label}
                  </button>
                </li>
              ))}
            </ul>
            
            <div className="mt-6 pt-4 border-t border-whisper-100">
              <p className="text-whisper-600 text-xs font-mono">
                secrets: {secretsFound.length}/7
              </p>
            </div>
          </div>
        </motion.nav>
      )}
    </AnimatePresence>
  );
}
