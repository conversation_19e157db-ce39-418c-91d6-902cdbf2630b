import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        lumon: {
          blue: "#1e3a8a",
          green: "#059669",
          beige: "#f5f5dc",
          gray: "#374151",
          "dark-blue": "#1e40af",
          "light-blue": "#3b82f6",
        },
        terminal: {
          green: "#00ff41",
          amber: "#ffb000",
          blue: "#4dd0e1",
          red: "#ef4444",
          "dark-green": "#00cc33",
          "bright-green": "#39ff14",
        },
        crt: {
          black: "#0a0a0a",
          "dark-gray": "#1a1a1a",
          "medium-gray": "#2a2a2a",
          "light-gray": "#3a3a3a",
          phosphor: "#00ff41",
          glow: "rgba(0, 255, 65, 0.3)",
        },
      },
      fontFamily: {
        mono: ["IBM Plex Mono", "Courier New", "monospace"],
        sans: ["IBM Plex Sans", "system-ui", "sans-serif"],
        terminal: ["IBM Plex Mono", "monospace"],
      },
      animation: {
        breathe: "breathe 4s ease-in-out infinite",
        glitch: "glitch 0.3s ease-in-out",
        "fade-in-slow": "fadeInSlow 3s ease-out",
        drift: "drift 20s linear infinite",
        flicker: "flicker 2s ease-in-out infinite",
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "crt-flicker": "crtFlicker 0.15s infinite linear",
        "terminal-blink": "terminalBlink 1s infinite",
        "lumon-glow": "lumonGlow 2s ease-in-out infinite alternate",
        "matrix-rain": "matrixRain 20s linear infinite",
        "letter-float": "letterFloat 6s ease-in-out infinite",
        "corporate-slide": "corporateSlide 0.8s ease-out",
      },
      keyframes: {
        breathe: {
          "0%, 100%": { opacity: "0.7", transform: "scale(1)" },
          "50%": { opacity: "1", transform: "scale(1.02)" },
        },
        glitch: {
          "0%": { transform: "translate(0)" },
          "20%": { transform: "translate(-2px, 2px)" },
          "40%": { transform: "translate(-2px, -2px)" },
          "60%": { transform: "translate(2px, 2px)" },
          "80%": { transform: "translate(2px, -2px)" },
          "100%": { transform: "translate(0)" },
        },
        fadeInSlow: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        drift: {
          "0%": { transform: "translateY(100vh) translateX(-10px)" },
          "100%": { transform: "translateY(-100px) translateX(10px)" },
        },
        flicker: {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.3" },
        },
        crtFlicker: {
          "0%": { opacity: "1" },
          "98%": { opacity: "1" },
          "99%": { opacity: "0.98" },
          "100%": { opacity: "1" },
        },
        terminalBlink: {
          "0%, 50%": { opacity: "1" },
          "51%, 100%": { opacity: "0" },
        },
        lumonGlow: {
          "0%": {
            textShadow:
              "0 0 5px var(--terminal-green), 0 0 10px var(--terminal-green)",
            filter: "brightness(1)",
          },
          "100%": {
            textShadow:
              "0 0 10px var(--terminal-green), 0 0 20px var(--terminal-green), 0 0 30px var(--terminal-green)",
            filter: "brightness(1.2)",
          },
        },
        letterFloat: {
          "0%, 100%": { transform: "translateY(0px) rotate(0deg)" },
          "25%": { transform: "translateY(-10px) rotate(1deg)" },
          "50%": { transform: "translateY(-5px) rotate(-1deg)" },
          "75%": { transform: "translateY(-15px) rotate(0.5deg)" },
        },
        corporateSlide: {
          "0%": { transform: "translateX(-100%)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
      },
      backdropBlur: {
        xs: "2px",
      },
    },
  },
  plugins: [],
};
export default config;
