import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        void: {
          50: '#1a1a1a',
          100: '#0f0f0f',
          200: '#080808',
          300: '#050505',
          400: '#030303',
          500: '#020202',
          600: '#010101',
          700: '#000000',
        },
        whisper: {
          50: '#f8f8f8',
          100: '#e8e8e8',
          200: '#d8d8d8',
          300: '#c8c8c8',
          400: '#a8a8a8',
          500: '#888888',
          600: '#686868',
          700: '#484848',
        },
        ghost: {
          50: 'rgba(255, 255, 255, 0.05)',
          100: 'rgba(255, 255, 255, 0.1)',
          200: 'rgba(255, 255, 255, 0.15)',
          300: 'rgba(255, 255, 255, 0.2)',
        },
        sepia: {
          50: '#2a2520',
          100: '#1f1c18',
          200: '#151310',
          300: '#0a0908',
        }
      },
      fontFamily: {
        'mono': ['JetBrains Mono', 'Courier New', 'monospace'],
        'whisper': ['Inter', 'system-ui', 'sans-serif'],
        'ghost': ['Times New Roman', 'serif'],
      },
      animation: {
        'breathe': 'breathe 4s ease-in-out infinite',
        'glitch': 'glitch 0.3s ease-in-out',
        'fade-in-slow': 'fadeInSlow 3s ease-out',
        'drift': 'drift 20s linear infinite',
        'flicker': 'flicker 2s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        breathe: {
          '0%, 100%': { opacity: '0.7', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.02)' },
        },
        glitch: {
          '0%': { transform: 'translate(0)' },
          '20%': { transform: 'translate(-2px, 2px)' },
          '40%': { transform: 'translate(-2px, -2px)' },
          '60%': { transform: 'translate(2px, 2px)' },
          '80%': { transform: 'translate(2px, -2px)' },
          '100%': { transform: 'translate(0)' },
        },
        fadeInSlow: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        drift: {
          '0%': { transform: 'translateY(100vh) translateX(-10px)' },
          '100%': { transform: 'translateY(-100px) translateX(10px)' },
        },
        flicker: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.3' },
        },
      },
      backdropBlur: {
        'xs': '2px',
      }
    },
  },
  plugins: [],
};
export default config;
