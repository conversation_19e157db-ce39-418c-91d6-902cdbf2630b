import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        void: {
          50: "#1a1a1a",
          100: "#0f0f0f",
          200: "#080808",
          300: "#050505",
          400: "#030303",
          500: "#020202",
          600: "#010101",
          700: "#000000",
        },
        whisper: {
          50: "#f8f8f8",
          100: "#e8e8e8",
          200: "#d8d8d8",
          300: "#c8c8c8",
          400: "#a8a8a8",
          500: "#888888",
          600: "#686868",
          700: "#484848",
        },
        ghost: {
          50: "rgba(255, 255, 255, 0.05)",
          100: "rgba(255, 255, 255, 0.1)",
          200: "rgba(255, 255, 255, 0.15)",
          300: "rgba(255, 255, 255, 0.2)",
          400: "rgba(255, 255, 255, 0.3)",
        },
        sepia: {
          50: "#2a2520",
          100: "#1f1c18",
          200: "#151310",
          300: "#0a0908",
        },
        dream: {
          blue: "#4a5568",
          purple: "#553c9a",
          gray: "#2d3748",
        },
      },
      fontFamily: {
        mono: ["IBM Plex Mono", "Courier New", "monospace"],
        sans: ["IBM Plex Sans", "system-ui", "sans-serif"],
        terminal: ["IBM Plex Mono", "monospace"],
      },
      animation: {
        breathe: "breathe 4s ease-in-out infinite",
        glitch: "glitch 0.3s ease-in-out",
        "fade-in-slow": "fadeInSlow 3s ease-out",
        drift: "drift 20s linear infinite",
        flicker: "flicker 2s ease-in-out infinite",
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "dreamy-float": "dreamyFloat 8s ease-in-out infinite",
        "gentle-sway": "gentleSway 12s ease-in-out infinite",
        "whisper-glow": "whisperGlow 3s ease-in-out infinite alternate",
        "depth-shift": "depthShift 15s ease-in-out infinite",
        "letter-dance": "letterDance 10s ease-in-out infinite",
        "ethereal-drift": "etherealDrift 20s linear infinite",
      },
      keyframes: {
        breathe: {
          "0%, 100%": { opacity: "0.7", transform: "scale(1)" },
          "50%": { opacity: "1", transform: "scale(1.02)" },
        },
        glitch: {
          "0%": { transform: "translate(0)" },
          "20%": { transform: "translate(-2px, 2px)" },
          "40%": { transform: "translate(-2px, -2px)" },
          "60%": { transform: "translate(2px, 2px)" },
          "80%": { transform: "translate(2px, -2px)" },
          "100%": { transform: "translate(0)" },
        },
        fadeInSlow: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        drift: {
          "0%": { transform: "translateY(100vh) translateX(-10px)" },
          "100%": { transform: "translateY(-100px) translateX(10px)" },
        },
        flicker: {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.3" },
        },
        dreamyFloat: {
          "0%, 100%": {
            transform: "translateY(0px) translateZ(0px) rotateX(0deg)",
            opacity: "0.8",
          },
          "25%": {
            transform: "translateY(-20px) translateZ(10px) rotateX(2deg)",
            opacity: "1",
          },
          "50%": {
            transform: "translateY(-10px) translateZ(-5px) rotateX(-1deg)",
            opacity: "0.9",
          },
          "75%": {
            transform: "translateY(-30px) translateZ(15px) rotateX(1deg)",
            opacity: "1",
          },
        },
        gentleSway: {
          "0%, 100%": {
            transform: "translateX(0px) rotate(0deg) scale(1)",
          },
          "33%": {
            transform: "translateX(-15px) rotate(-2deg) scale(1.02)",
          },
          "66%": {
            transform: "translateX(15px) rotate(2deg) scale(0.98)",
          },
        },
        whisperGlow: {
          "0%": {
            textShadow: "0 0 5px rgba(255, 255, 255, 0.3)",
            filter: "brightness(1)",
          },
          "100%": {
            textShadow:
              "0 0 20px rgba(255, 255, 255, 0.6), 0 0 40px rgba(255, 255, 255, 0.3)",
            filter: "brightness(1.3)",
          },
        },
        depthShift: {
          "0%, 100%": {
            transform: "perspective(1000px) rotateY(0deg) rotateX(0deg)",
          },
          "25%": {
            transform: "perspective(1000px) rotateY(5deg) rotateX(2deg)",
          },
          "50%": {
            transform: "perspective(1000px) rotateY(0deg) rotateX(-2deg)",
          },
          "75%": {
            transform: "perspective(1000px) rotateY(-5deg) rotateX(1deg)",
          },
        },
        letterDance: {
          "0%, 100%": {
            transform: "translateY(0px) translateX(0px) rotate(0deg)",
          },
          "20%": {
            transform: "translateY(-8px) translateX(3px) rotate(1deg)",
          },
          "40%": {
            transform: "translateY(-15px) translateX(-2px) rotate(-0.5deg)",
          },
          "60%": {
            transform: "translateY(-5px) translateX(5px) rotate(1.5deg)",
          },
          "80%": {
            transform: "translateY(-12px) translateX(-3px) rotate(-1deg)",
          },
        },
        etherealDrift: {
          "0%": {
            transform: "translateY(100vh) translateX(-20px) rotate(0deg)",
            opacity: "0",
          },
          "10%": {
            opacity: "0.3",
          },
          "90%": {
            opacity: "0.3",
          },
          "100%": {
            transform: "translateY(-100px) translateX(20px) rotate(360deg)",
            opacity: "0",
          },
        },
      },
      backdropBlur: {
        xs: "2px",
      },
    },
  },
  plugins: [],
};
export default config;
