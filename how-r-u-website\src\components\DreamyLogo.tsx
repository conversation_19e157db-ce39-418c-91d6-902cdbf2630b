'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface DreamyLogoProps {
  onLetterClick?: (letter: string, index: number) => void;
}

export function DreamyLogo({ onLetterClick }: DreamyLogoProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [clickSequence, setClickSequence] = useState<number[]>([]);
  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);
  const logoRef = useRef<SVGSVGElement>(null);

  const letters = ['h', 'o', 'w', 'r', 'u'];
  const secretSequence = [0, 4, 1, 3, 2]; // h-u-o-r-w

  const handleLetterClick = (letter: string, index: number) => {
    const newSequence = [...clickSequence, index];
    setClickSequence(newSequence);

    // Activate letter with dreamy glow
    const newActiveLetters = [...activeLetters];
    newActiveLetters[index] = true;
    setActiveLetters(newActiveLetters);

    // Reset after 3 seconds
    setTimeout(() => {
      const resetLetters = [...activeLetters];
      resetLetters[index] = false;
      setActiveLetters(resetLetters);
    }, 3000);

    // Check if sequence matches secret pattern
    if (newSequence.length === secretSequence.length) {
      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);
      if (isCorrect) {
        onLetterClick?.('secret', -1);
      }
      setTimeout(() => setClickSequence([]), 1000);
    }

    onLetterClick?.(letter, index);
  };

  const letterVariants = {
    initial: { 
      opacity: 0.7, 
      scale: 1,
      rotateX: 0,
      rotateY: 0,
      z: 0,
    },
    hover: { 
      opacity: 1, 
      scale: 1.1,
      rotateX: 10,
      rotateY: 5,
      z: 20,
      transition: { duration: 0.6, ease: "easeOut" }
    },
    click: { 
      opacity: [1, 0.3, 1], 
      scale: [1, 0.8, 1.2, 1],
      rotateX: [0, 15, -10, 0],
      rotateY: [0, -15, 10, 0],
      transition: { duration: 0.8, ease: "easeInOut" }
    },
    active: {
      opacity: 1,
      scale: 1.05,
      filter: 'brightness(1.5) blur(0.5px)',
      textShadow: '0 0 20px rgba(255, 255, 255, 0.8)',
    }
  };

  return (
    <div className="relative perspective-1000">
      {/* Dreamy background glow */}
      <motion.div
        className="absolute inset-0 -m-20"
        animate={{
          background: [
            'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
            'radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%)',
            'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
          ]
        }}
        transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* 3D Logo Container */}
      <motion.div
        className="relative transform-gpu"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        animate={{
          rotateX: isHovered ? 5 : 0,
          rotateY: isHovered ? 5 : 0,
          scale: isHovered ? 1.05 : 1,
        }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        style={{ 
          transformStyle: 'preserve-3d',
          perspective: '1000px'
        }}
      >
        <svg
          ref={logoRef}
          width="266"
          height="275"
          viewBox="0 0 266 275"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-80 h-auto cursor-pointer filter drop-shadow-2xl"
          style={{
            filter: isHovered 
              ? 'brightness(1.3) drop-shadow(0 0 30px rgba(255, 255, 255, 0.5))' 
              : 'brightness(1) drop-shadow(0 0 15px rgba(255, 255, 255, 0.2))',
            transition: 'filter 0.6s ease'
          }}
        >
          <g id="howru">
            {/* Letter H */}
            <motion.path
              id="h"
              d="M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z"
              fill={activeLetters[0] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)"}
              variants={letterVariants}
              initial="initial"
              whileHover="hover"
              whileTap="click"
              animate={activeLetters[0] ? "active" : "initial"}
              onClick={() => handleLetterClick('h', 0)}
              className="cursor-pointer animate-letter-dance"
              style={{ 
                animationDelay: '0s',
                transformOrigin: 'center',
                filter: activeLetters[0] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
              }}
            />
            
            {/* Letter O */}
            <motion.path
              id="o"
              d="M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z"
              fill={activeLetters[1] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)"}
              variants={letterVariants}
              initial="initial"
              whileHover="hover"
              whileTap="click"
              animate={activeLetters[1] ? "active" : "initial"}
              onClick={() => handleLetterClick('o', 1)}
              className="cursor-pointer animate-dreamy-float"
              style={{ 
                animationDelay: '1s',
                transformOrigin: 'center',
                filter: activeLetters[1] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
              }}
            />
            
            {/* Letter W */}
            <motion.path
              id="w"
              d="M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z"
              fill={activeLetters[2] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)"}
              variants={letterVariants}
              initial="initial"
              whileHover="hover"
              whileTap="click"
              animate={activeLetters[2] ? "active" : "initial"}
              onClick={() => handleLetterClick('w', 2)}
              className="cursor-pointer animate-gentle-sway"
              style={{ 
                animationDelay: '2s',
                transformOrigin: 'center',
                filter: activeLetters[2] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
              }}
            />
            
            {/* Letter R */}
            <motion.path
              id="r"
              d="M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z"
              fill={activeLetters[3] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)"}
              variants={letterVariants}
              initial="initial"
              whileHover="hover"
              whileTap="click"
              animate={activeLetters[3] ? "active" : "initial"}
              onClick={() => handleLetterClick('r', 3)}
              className="cursor-pointer animate-depth-shift"
              style={{ 
                animationDelay: '3s',
                transformOrigin: 'center',
                filter: activeLetters[3] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
              }}
            />
            
            {/* Letter U */}
            <motion.path
              id="u"
              d="M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z"
              fill={activeLetters[4] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)"}
              variants={letterVariants}
              initial="initial"
              whileHover="hover"
              whileTap="click"
              animate={activeLetters[4] ? "active" : "initial"}
              onClick={() => handleLetterClick('u', 4)}
              className="cursor-pointer animate-whisper-glow"
              style={{ 
                animationDelay: '4s',
                transformOrigin: 'center',
                filter: activeLetters[4] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
              }}
            />
          </g>
        </svg>

        {/* 3D depth layers */}
        <motion.div
          className="absolute inset-0 -z-10"
          animate={{
            rotateX: isHovered ? 3 : 0,
            rotateY: isHovered ? 3 : 0,
            scale: isHovered ? 1.02 : 1,
            opacity: isHovered ? 0.3 : 0.1,
          }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          style={{ 
            transformStyle: 'preserve-3d',
            transform: 'translateZ(-20px)',
            filter: 'blur(2px)',
            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)'
          }}
        />
      </motion.div>

      {/* Floating particles around logo */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        animate={{
          rotate: 360,
        }}
        transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
      >
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-30"
            style={{
              left: `${20 + Math.cos(i * 45 * Math.PI / 180) * 150}px`,
              top: `${20 + Math.sin(i * 45 * Math.PI / 180) * 150}px`,
            }}
            animate={{
              opacity: [0.1, 0.5, 0.1],
              scale: [0.5, 1.5, 0.5],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.3,
            }}
          />
        ))}
      </motion.div>
    </div>
  );
}
