@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@300;400;500;600&family=IBM+Plex+Sans:wght@300;400;500;600&display=swap");
@import "tailwindcss";

:root {
  /* Dreamy "how r u" Colors */
  --void-deep: #000000;
  --void-medium: #0a0a0a;
  --whisper-faint: rgba(255, 255, 255, 0.05);
  --whisper-soft: rgba(255, 255, 255, 0.1);
  --whisper-visible: rgba(255, 255, 255, 0.2);
  --whisper-bright: rgba(255, 255, 255, 0.4);
  --dream-glow: rgba(255, 255, 255, 0.6);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  background: var(--void-deep);
  color: var(--whisper-visible);
  font-family: "IBM Plex Sans", system-ui, sans-serif;
  font-weight: 300;
  line-height: 1.6;
  overflow-x: hidden;
  cursor: none;
  position: relative;
}

/* Dreamy atmospheric overlay */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 30%,
      rgba(255, 255, 255, 0.02) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 70%,
      rgba(255, 255, 255, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: 1;
  animation: dreamyShift 20s ease-in-out infinite;
}

@keyframes dreamyShift {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* Custom cursor */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--whisper-soft);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.2s ease;
  box-shadow: 0 0 15px var(--whisper-bright);
  animation: whisper-glow 3s ease-in-out infinite alternate;
}

/* Glitch effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: rgba(255, 0, 0, 0.3);
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: rgba(0, 255, 255, 0.3);
  z-index: -2;
}

@keyframes glitch-1 {
  0%,
  14%,
  15%,
  49%,
  50%,
  99%,
  100% {
    transform: translate(0);
  }
  15%,
  49% {
    transform: translate(-2px, -1px);
  }
}

@keyframes glitch-2 {
  0%,
  20%,
  21%,
  62%,
  63%,
  99%,
  100% {
    transform: translate(0);
  }
  21%,
  62% {
    transform: translate(2px, 1px);
  }
}

/* Dust particles */
.dust-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: drift 20s linear infinite;
}

/* Hidden elements that appear on interaction */
.hidden-element {
  opacity: 0;
  transition: opacity 0.5s ease;
}

.hidden-element.revealed {
  opacity: 1;
}

/* Breathing text */
.breathing-text {
  animation: breathe 4s ease-in-out infinite;
}

/* Typewriter effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--void-deep);
}

::-webkit-scrollbar-thumb {
  background: var(--whisper-faint);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--whisper-soft);
}
