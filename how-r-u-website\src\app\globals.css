@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500&family=Inter:wght@300;400;500&display=swap");
@import "tailwindcss";

:root {
  --void-deep: #000000;
  --void-medium: #0a0a0a;
  --whisper-faint: rgba(255, 255, 255, 0.05);
  --whisper-soft: rgba(255, 255, 255, 0.1);
  --whisper-visible: rgba(255, 255, 255, 0.2);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  background: var(--void-deep);
  color: var(--whisper-visible);
  font-family: "Inter", system-ui, sans-serif;
  font-weight: 300;
  line-height: 1.6;
  overflow-x: hidden;
  cursor: none;
}

/* Custom cursor */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

/* Glitch effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: rgba(255, 0, 0, 0.3);
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: rgba(0, 255, 255, 0.3);
  z-index: -2;
}

@keyframes glitch-1 {
  0%,
  14%,
  15%,
  49%,
  50%,
  99%,
  100% {
    transform: translate(0);
  }
  15%,
  49% {
    transform: translate(-2px, -1px);
  }
}

@keyframes glitch-2 {
  0%,
  20%,
  21%,
  62%,
  63%,
  99%,
  100% {
    transform: translate(0);
  }
  21%,
  62% {
    transform: translate(2px, 1px);
  }
}

/* Dust particles */
.dust-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: drift 20s linear infinite;
}

/* Hidden elements that appear on interaction */
.hidden-element {
  opacity: 0;
  transition: opacity 0.5s ease;
}

.hidden-element.revealed {
  opacity: 1;
}

/* Breathing text */
.breathing-text {
  animation: breathe 4s ease-in-out infinite;
}

/* Typewriter effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--void-deep);
}

::-webkit-scrollbar-thumb {
  background: var(--whisper-faint);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--whisper-soft);
}
