@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@300;400;500;600&family=IBM+Plex+Sans:wght@300;400;500;600&display=swap");
@import "tailwindcss";

:root {
  /* Lumon Corporate Colors */
  --lumon-blue: #1e3a8a;
  --lumon-green: #059669;
  --lumon-beige: #f5f5dc;
  --lumon-gray: #374151;
  --terminal-green: #00ff41;
  --terminal-amber: #ffb000;
  --crt-blue: #4dd0e1;
  --error-red: #ef4444;

  /* CRT Monitor Effects */
  --scanline-opacity: 0.04;
  --flicker-intensity: 0.02;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  background: #0a0a0a;
  color: var(--terminal-green);
  font-family: "IBM Plex Mono", monospace;
  font-weight: 400;
  line-height: 1.4;
  overflow-x: hidden;
  cursor: none;

  /* CRT Monitor Effect */
  position: relative;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 65, var(--scanline-opacity)) 2px,
    rgba(0, 255, 65, var(--scanline-opacity)) 4px
  );
  pointer-events: none;
  z-index: 1000;
  animation: scanlines 0.1s linear infinite;
}

body::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at center,
    transparent 0%,
    rgba(0, 0, 0, 0.3) 100%
  );
  pointer-events: none;
  z-index: 999;
}

@keyframes scanlines {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(4px);
  }
}

/* Custom cursor */
.custom-cursor {
  position: fixed;
  width: 16px;
  height: 16px;
  background: var(--terminal-green);
  border: 1px solid var(--terminal-green);
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  box-shadow: 0 0 10px var(--terminal-green);
  animation: lumon-glow 2s ease-in-out infinite alternate;
}

/* Glitch effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: rgba(255, 0, 0, 0.3);
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: rgba(0, 255, 255, 0.3);
  z-index: -2;
}

@keyframes glitch-1 {
  0%,
  14%,
  15%,
  49%,
  50%,
  99%,
  100% {
    transform: translate(0);
  }
  15%,
  49% {
    transform: translate(-2px, -1px);
  }
}

@keyframes glitch-2 {
  0%,
  20%,
  21%,
  62%,
  63%,
  99%,
  100% {
    transform: translate(0);
  }
  21%,
  62% {
    transform: translate(2px, 1px);
  }
}

/* Dust particles */
.dust-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: drift 20s linear infinite;
}

/* Hidden elements that appear on interaction */
.hidden-element {
  opacity: 0;
  transition: opacity 0.5s ease;
}

.hidden-element.revealed {
  opacity: 1;
}

/* Breathing text */
.breathing-text {
  animation: breathe 4s ease-in-out infinite;
}

/* Typewriter effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--void-deep);
}

::-webkit-scrollbar-thumb {
  background: var(--whisper-faint);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--whisper-soft);
}
