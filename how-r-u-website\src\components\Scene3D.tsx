'use client';

import { useRef, useState } from 'react';
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';
import { 
  Text3D, 
  Center, 
  Float, 
  MeshDistortMaterial, 
  Sphere, 
  Environment,
  OrbitControls,
  Sparkles,
  Stars,
  Cloud
} from '@react-three/drei';
import { EffectComposer, Bloom, ChromaticAberration, Glitch } from '@react-three/postprocessing';
import { BlendFunction } from 'postprocessing';
import * as THREE from 'three';

function FloatingText({ text, position, color = "#ffffff", isActive = false }: {
  text: string;
  position: [number, number, number];
  color?: string;
  isActive?: boolean;
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.5;
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.3;
      
      if (isActive) {
        meshRef.current.scale.setScalar(1.5 + Math.sin(state.clock.elapsedTime * 5) * 0.2);
      }
    }
  });

  return (
    <Float speed={3} rotationIntensity={0.5} floatIntensity={2}>
      <Text3D
        ref={meshRef}
        font="/fonts/helvetiker_regular.typeface.json"
        size={2}
        height={0.5}
        position={position}
        curveSegments={12}
      >
        {text}
        <MeshDistortMaterial
          color={color}
          distort={isActive ? 0.6 : 0.2}
          speed={isActive ? 5 : 2}
          roughness={0.1}
          metalness={0.8}
        />
      </Text3D>
    </Float>
  );
}

function DreamSphere() {
  const sphereRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (sphereRef.current) {
      sphereRef.current.rotation.x = state.clock.elapsedTime * 0.1;
      sphereRef.current.rotation.y = state.clock.elapsedTime * 0.15;
      sphereRef.current.position.y = Math.sin(state.clock.elapsedTime) * 2;
    }
  });

  return (
    <Sphere ref={sphereRef} args={[3, 64, 64]} position={[0, 0, -8]}>
      <MeshDistortMaterial
        color="#4a5568"
        distort={0.4}
        speed={2}
        roughness={0.2}
        metalness={0.9}
        transparent
        opacity={0.6}
      />
    </Sphere>
  );
}

function ParticleSystem() {
  const pointsRef = useRef<THREE.Points>(null);
  const particleCount = 500;
  
  const positions = new Float32Array(particleCount * 3);
  const colors = new Float32Array(particleCount * 3);
  
  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 50;
    positions[i * 3 + 1] = (Math.random() - 0.5) * 50;
    positions[i * 3 + 2] = (Math.random() - 0.5) * 50;
    
    colors[i * 3] = Math.random();
    colors[i * 3 + 1] = Math.random();
    colors[i * 3 + 2] = Math.random();
  }

  useFrame((state) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;
      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;
    }
  });

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.05}
        vertexColors
        transparent
        opacity={0.8}
        sizeAttenuation
      />
    </points>
  );
}

function CameraAnimation() {
  const { camera } = useThree();
  
  useFrame((state) => {
    camera.position.x = Math.sin(state.clock.elapsedTime * 0.2) * 5;
    camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 3;
    camera.position.z = 15 + Math.sin(state.clock.elapsedTime * 0.1) * 5;
    camera.lookAt(0, 0, 0);
  });
  
  return null;
}

interface Scene3DProps {
  activeLetters?: boolean[];
}

export function Scene3D({ activeLetters = [false, false, false, false, false] }: Scene3DProps) {
  const [glitchActive, setGlitchActive] = useState(false);
  
  const letters = ['h', 'o', 'w', 'r', 'u'];
  const positions: [number, number, number][] = [
    [-8, 2, 0],
    [-4, -1, 2],
    [0, 3, -1],
    [4, -2, 1],
    [8, 1, -2]
  ];

  return (
    <div className="w-full h-screen">
      <Canvas
        camera={{ position: [0, 0, 15], fov: 75 }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* Éclairage dramatique */}
        <ambientLight intensity={0.2} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#ffffff" />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#4a5568" />
        <spotLight
          position={[0, 20, 0]}
          angle={0.3}
          penumbra={1}
          intensity={1}
          castShadow
        />
        
        {/* Environnement */}
        <Environment preset="night" />
        <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade />
        
        {/* Nuages atmosphériques */}
        <Cloud
          position={[-20, 10, -20]}
          speed={0.2}
          opacity={0.1}
          width={10}
          depth={1.5}
          segments={20}
        />
        <Cloud
          position={[20, -10, -20]}
          speed={0.3}
          opacity={0.1}
          width={15}
          depth={2}
          segments={25}
        />
        
        {/* Système de particules */}
        <ParticleSystem />
        <Sparkles count={100} scale={[20, 20, 20]} size={2} speed={0.5} />
        
        {/* Sphère de rêve */}
        <DreamSphere />
        
        {/* Lettres 3D flottantes */}
        <Center>
          {letters.map((letter, index) => (
            <FloatingText
              key={letter}
              text={letter}
              position={positions[index]}
              color={activeLetters[index] ? "#ffffff" : "#a0a0a0"}
              isActive={activeLetters[index]}
            />
          ))}
        </Center>
        
        {/* Animation de caméra */}
        <CameraAnimation />
        
        {/* Contrôles optionnels */}
        <OrbitControls
          enablePan={false}
          enableZoom={false}
          enableRotate={true}
          autoRotate
          autoRotateSpeed={0.5}
        />
        
        {/* Effets de post-processing */}
        <EffectComposer>
          <Bloom
            intensity={1.5}
            luminanceThreshold={0.2}
            luminanceSmoothing={0.9}
            height={300}
          />
          <ChromaticAberration
            blendFunction={BlendFunction.NORMAL}
            offset={[0.002, 0.002]}
          />
          {glitchActive && (
            <Glitch
              delay={[1.5, 3.5]}
              duration={[0.6, 1.0]}
              strength={[0.3, 1.0]}
              mode={0}
            />
          )}
        </EffectComposer>
      </Canvas>
      
      {/* Contrôles UI */}
      <div className="absolute bottom-8 left-8 space-y-2">
        <button
          onClick={() => setGlitchActive(!glitchActive)}
          className="px-4 py-2 bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light hover:bg-ghost-200 transition-colors"
        >
          {glitchActive ? 'Désactiver Glitch' : 'Activer Glitch'}
        </button>
      </div>
    </div>
  );
}
