import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { CustomCursor } from "@/components/CustomCursor";
import { DustParticles } from "@/components/DustParticles";

export const metadata: Metadata = {
  title: "how r u",
  description: "a digital séance with consciousness",
  keywords: ["music", "artist", "experimental", "ambient", "introspective"],
  openGraph: {
    title: "how r u",
    description: "a digital séance with consciousness",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <DigitalRain />
        <CustomCursor />
        <DustParticles />
        {children}
      </body>
    </html>
  );
}
