'use client';

import { motion } from 'framer-motion';
import { useResponsive } from './ResponsiveLayout';

interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
}

export function EnhancedButton({ 
  children, 
  onClick, 
  variant = 'primary', 
  size = 'md', 
  className = '',
  disabled = false 
}: EnhancedButtonProps) {
  const { isMobile } = useResponsive();
  
  const baseClasses = "font-light transition-all duration-300 transform backdrop-blur-sm border";
  
  const variantClasses = {
    primary: "bg-ghost-100 border-whisper-200 text-whisper-300 hover:bg-ghost-200 hover:border-whisper-300",
    secondary: "bg-whisper-100 border-whisper-300 text-void-700 hover:bg-whisper-200",
    ghost: "bg-transparent border-whisper-200 text-whisper-400 hover:bg-ghost-100 hover:text-whisper-300"
  };
  
  const sizeClasses = {
    sm: isMobile ? "px-3 py-1 text-xs" : "px-4 py-2 text-sm",
    md: isMobile ? "px-4 py-2 text-sm" : "px-6 py-3 text-base",
    lg: isMobile ? "px-6 py-3 text-base" : "px-8 py-4 text-lg"
  };
  
  return (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={`
        ${baseClasses} 
        ${variantClasses[variant]} 
        ${sizeClasses[size]} 
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-lg animate-glow-pulse'}
        ${className}
      `}
      whileHover={disabled ? {} : { scale: 1.05 }}
      whileTap={disabled ? {} : { scale: 0.95 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.button>
  );
}

interface EnhancedCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  onClick?: () => void;
  className?: string;
  glowing?: boolean;
}

export function EnhancedCard({ 
  children, 
  title, 
  subtitle, 
  onClick, 
  className = '',
  glowing = false 
}: EnhancedCardProps) {
  const { isMobile } = useResponsive();
  
  return (
    <motion.div
      onClick={onClick}
      className={`
        border border-whisper-200 backdrop-blur-sm transition-all duration-700
        ${onClick ? 'cursor-pointer hover:border-whisper-300 hover:bg-ghost-100' : ''}
        ${glowing ? 'animate-glow-pulse' : ''}
        ${isMobile ? 'p-6' : 'p-8'}
        ${className}
      `}
      whileHover={onClick ? { scale: 1.02, y: -5 } : {}}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {title && (
        <h3 className="text-whisper-300 text-xl font-light mb-4 animate-whisper-glow">
          {title}
        </h3>
      )}
      {subtitle && (
        <p className="text-whisper-500 text-sm opacity-80 mb-4">
          {subtitle}
        </p>
      )}
      {children}
      <div className="mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-shimmer" />
    </motion.div>
  );
}

interface FloatingElementProps {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
}

export function FloatingElement({ 
  children, 
  delay = 0, 
  duration = 6, 
  className = '' 
}: FloatingElementProps) {
  return (
    <motion.div
      className={`animate-float3d ${className}`}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 1 }}
      style={{
        animationDelay: `${delay}s`,
        animationDuration: `${duration}s`
      }}
    >
      {children}
    </motion.div>
  );
}

interface GlowTextProps {
  children: React.ReactNode;
  intensity?: 'low' | 'medium' | 'high';
  color?: string;
  className?: string;
}

export function GlowText({ 
  children, 
  intensity = 'medium', 
  color = 'var(--whisper-bright)', 
  className = '' 
}: GlowTextProps) {
  const intensityMap = {
    low: '0 0 10px',
    medium: '0 0 20px',
    high: '0 0 30px, 0 0 40px'
  };
  
  return (
    <span
      className={`animate-whisper-glow ${className}`}
      style={{
        textShadow: `${intensityMap[intensity]} ${color}`
      }}
    >
      {children}
    </span>
  );
}

interface ProgressBarProps {
  progress: number;
  max: number;
  label?: string;
  color?: string;
  className?: string;
}

export function ProgressBar({ 
  progress, 
  max, 
  label, 
  color = '#64ffda', 
  className = '' 
}: ProgressBarProps) {
  const percentage = (progress / max) * 100;
  
  return (
    <div className={`w-full ${className}`}>
      {label && (
        <div className="flex justify-between text-whisper-400 text-xs mb-2">
          <span>{label}</span>
          <span>{progress}/{max}</span>
        </div>
      )}
      <div className="w-full bg-ghost-100 rounded-full h-2 overflow-hidden">
        <motion.div
          className="h-full rounded-full"
          style={{ backgroundColor: color }}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        />
      </div>
    </div>
  );
}

interface ParticleFieldProps {
  count?: number;
  className?: string;
}

export function ParticleField({ count = 20, className = '' }: ParticleFieldProps) {
  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-whisper-300 rounded-full opacity-30"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.8, 0.3],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            repeat: Infinity,
            delay: Math.random() * 2,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}

interface StatusIndicatorProps {
  status: 'idle' | 'loading' | 'success' | 'error';
  message?: string;
  className?: string;
}

export function StatusIndicator({ status, message, className = '' }: StatusIndicatorProps) {
  const statusConfig = {
    idle: { color: 'text-whisper-500', icon: '○' },
    loading: { color: 'text-blue-400', icon: '◐' },
    success: { color: 'text-green-400', icon: '●' },
    error: { color: 'text-red-400', icon: '✕' }
  };
  
  const config = statusConfig[status];
  
  return (
    <motion.div
      className={`flex items-center space-x-2 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <motion.span
        className={`${config.color} text-sm`}
        animate={status === 'loading' ? { rotate: 360 } : {}}
        transition={status === 'loading' ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
      >
        {config.icon}
      </motion.span>
      {message && (
        <span className="text-whisper-400 text-xs font-light">
          {message}
        </span>
      )}
    </motion.div>
  );
}
