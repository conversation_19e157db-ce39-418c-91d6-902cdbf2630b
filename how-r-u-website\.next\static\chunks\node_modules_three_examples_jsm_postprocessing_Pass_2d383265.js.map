{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three/examples/jsm/postprocessing/Pass.js"], "sourcesContent": ["import {\n\t<PERSON>ufferGeometry,\n\tFloat32BufferAttribute,\n\tOrthographicCamera,\n\tMesh\n} from 'three';\n\n/**\n * Abstract base class for all post processing passes.\n *\n * This module is only relevant for post processing with {@link WebGLRenderer}.\n *\n * @abstract\n * @three_import import { Pass } from 'three/addons/postprocessing/Pass.js';\n */\nclass Pass {\n\n\t/**\n\t * Constructs a new pass.\n\t */\n\tconstructor() {\n\n\t\t/**\n\t\t * This flag can be used for type testing.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @readonly\n\t\t * @default true\n\t\t */\n\t\tthis.isPass = true;\n\n\t\t/**\n\t\t * If set to `true`, the pass is processed by the composer.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.enabled = true;\n\n\t\t/**\n\t\t * If set to `true`, the pass indicates to swap read and write buffer after rendering.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default true\n\t\t */\n\t\tthis.needsSwap = true;\n\n\t\t/**\n\t\t * If set to `true`, the pass clears its buffer before rendering\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.clear = false;\n\n\t\t/**\n\t\t * If set to `true`, the result of the pass is rendered to screen. The last pass in the composers\n\t\t * pass chain gets automatically rendered to screen, no matter how this property is configured.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @default false\n\t\t */\n\t\tthis.renderToScreen = false;\n\n\t}\n\n\t/**\n\t * Sets the size of the pass.\n\t *\n\t * @abstract\n\t * @param {number} width - The width to set.\n\t * @param {number} height - The width to set.\n\t */\n\tsetSize( /* width, height */ ) {}\n\n\t/**\n\t * This method holds the render logic of a pass. It must be implemented in all derived classes.\n\t *\n\t * @abstract\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t * @param {WebGLRenderTarget} writeBuffer - The write buffer. This buffer is intended as the rendering\n\t * destination for the pass.\n\t * @param {WebGLRenderTarget} readBuffer - The read buffer. The pass can access the result from the\n\t * previous pass from this buffer.\n\t * @param {number} deltaTime - The delta time in seconds.\n\t * @param {boolean} maskActive - Whether masking is active or not.\n\t */\n\trender( /* renderer, writeBuffer, readBuffer, deltaTime, maskActive */ ) {\n\n\t\tconsole.error( 'THREE.Pass: .render() must be implemented in derived pass.' );\n\n\t}\n\n\t/**\n\t * Frees the GPU-related resources allocated by this instance. Call this\n\t * method whenever the pass is no longer used in your app.\n\t *\n\t * @abstract\n\t */\n\tdispose() {}\n\n}\n\n// Helper for passes that need to fill the viewport with a single quad.\n\nconst _camera = new OrthographicCamera( - 1, 1, 1, - 1, 0, 1 );\n\n// https://github.com/mrdoob/three.js/pull/21358\n\nclass FullscreenTriangleGeometry extends BufferGeometry {\n\n\tconstructor() {\n\n\t\tsuper();\n\n\t\tthis.setAttribute( 'position', new Float32BufferAttribute( [ - 1, 3, 0, - 1, - 1, 0, 3, - 1, 0 ], 3 ) );\n\t\tthis.setAttribute( 'uv', new Float32BufferAttribute( [ 0, 2, 0, 0, 2, 0 ], 2 ) );\n\n\t}\n\n}\n\nconst _geometry = new FullscreenTriangleGeometry();\n\n\n/**\n * This module is a helper for passes which need to render a full\n * screen effect which is quite common in context of post processing.\n *\n * The intended usage is to reuse a single full screen quad for rendering\n * subsequent passes by just reassigning the `material` reference.\n *\n * This module can only be used with {@link WebGLRenderer}.\n *\n * @augments Mesh\n * @three_import import { FullScreenQuad } from 'three/addons/postprocessing/Pass.js';\n */\nclass FullScreenQuad {\n\n\t/**\n\t * Constructs a new full screen quad.\n\t *\n\t * @param {?Material} material - The material to render te full screen quad with.\n\t */\n\tconstructor( material ) {\n\n\t\tthis._mesh = new Mesh( _geometry, material );\n\n\t}\n\n\t/**\n\t * Frees the GPU-related resources allocated by this instance. Call this\n\t * method whenever the instance is no longer used in your app.\n\t */\n\tdispose() {\n\n\t\tthis._mesh.geometry.dispose();\n\n\t}\n\n\t/**\n\t * Renders the full screen quad.\n\t *\n\t * @param {WebGLRenderer} renderer - The renderer.\n\t */\n\trender( renderer ) {\n\n\t\trenderer.render( this._mesh, _camera );\n\n\t}\n\n\t/**\n\t * The quad's material.\n\t *\n\t * @type {?Material}\n\t */\n\tget material() {\n\n\t\treturn this._mesh.material;\n\n\t}\n\n\tset material( value ) {\n\n\t\tthis._mesh.material = value;\n\n\t}\n\n}\n\nexport { Pass, FullScreenQuad };\n"], "names": [], "mappings": ";;;;AAAA;;AAOA;;;;;;;CAOC,GACD,MAAM;IAEL;;EAEC,GACD,aAAc;QAEb;;;;;;GAMC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;;;;GAKC,GACD,IAAI,CAAC,OAAO,GAAG;QAEf;;;;;GAKC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;;;;GAKC,GACD,IAAI,CAAC,KAAK,GAAG;QAEb;;;;;;GAMC,GACD,IAAI,CAAC,cAAc,GAAG;IAEvB;IAEA;;;;;;EAMC,GACD,UAA+B,CAAC;IAEhC;;;;;;;;;;;EAWC,GACD,SAAyE;QAExE,QAAQ,KAAK,CAAE;IAEhB;IAEA;;;;;EAKC,GACD,UAAU,CAAC;AAEZ;AAEA,uEAAuE;AAEvE,MAAM,UAAU,IAAI,kJAAA,CAAA,qBAAkB,CAAE,CAAE,GAAG,GAAG,GAAG,CAAE,GAAG,GAAG;AAE3D,gDAAgD;AAEhD,MAAM,mCAAmC,kJAAA,CAAA,iBAAc;IAEtD,aAAc;QAEb,KAAK;QAEL,IAAI,CAAC,YAAY,CAAE,YAAY,IAAI,kJAAA,CAAA,yBAAsB,CAAE;YAAE,CAAE;YAAG;YAAG;YAAG,CAAE;YAAG,CAAE;YAAG;YAAG;YAAG,CAAE;YAAG;SAAG,EAAE;QAClG,IAAI,CAAC,YAAY,CAAE,MAAM,IAAI,kJAAA,CAAA,yBAAsB,CAAE;YAAE;YAAG;YAAG;YAAG;YAAG;YAAG;SAAG,EAAE;IAE5E;AAED;AAEA,MAAM,YAAY,IAAI;AAGtB;;;;;;;;;;;CAWC,GACD,MAAM;IAEL;;;;EAIC,GACD,YAAa,QAAQ,CAAG;QAEvB,IAAI,CAAC,KAAK,GAAG,IAAI,kJAAA,CAAA,OAAI,CAAE,WAAW;IAEnC;IAEA;;;EAGC,GACD,UAAU;QAET,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO;IAE5B;IAEA;;;;EAIC,GACD,OAAQ,QAAQ,EAAG;QAElB,SAAS,MAAM,CAAE,IAAI,CAAC,KAAK,EAAE;IAE9B;IAEA;;;;EAIC,GACD,IAAI,WAAW;QAEd,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAE3B;IAEA,IAAI,SAAU,KAAK,EAAG;QAErB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IAEvB;AAED", "ignoreList": [0], "debugId": null}}]}