"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>monLogo } from "@/components/LumonLogo";
import { LumonTerminal } from "@/components/LumonTerminal";
import { EasterEggHunter } from "@/components/EasterEggHunter";
import { AmbientSoundscape } from "@/components/AmbientSoundscape";
import { HiddenNavigation } from "@/components/HiddenNavigation";

export default function Home() {
  const [currentPhase, setCurrentPhase] = useState<
    "entry" | "void" | "exploration"
  >("entry");
  const [secretsFound, setSecretsFound] = useState<string[]>([]);
  const [showHiddenText, setShowHiddenText] = useState(false);

  useEffect(() => {
    // Auto-transition from entry to void after 3 seconds
    const timer = setTimeout(() => {
      if (currentPhase === "entry") {
        setCurrentPhase("void");
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [currentPhase]);

  const handleLogoInteraction = (letter: string, index: number) => {
    if (letter === "secret") {
      setSecretsFound((prev) => [...prev, "logo_sequence"]);
      setCurrentPhase("exploration");
    }
  };

  const handleSecretFound = (secretId: string) => {
    setSecretsFound((prev) => [...prev, secretId]);
  };

  return (
    <div className="min-h-screen bg-crt-black relative overflow-hidden">
      <AmbientSoundscape />
      <EasterEggHunter onSecretFound={handleSecretFound} />
      <AnimatePresence mode="wait">
        {currentPhase === "entry" && (
          <motion.div
            key="entry"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2 }}
            className="fixed inset-0 flex items-center justify-center z-10"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, duration: 1.5 }}
              className="text-center"
            >
              <div className="text-terminal-green text-sm font-mono mb-8 animate-crt-flicker">
                LUMON INDUSTRIES - MACRODATA REFINEMENT DIVISION
              </div>
              <h1
                className="text-terminal-green text-6xl font-mono font-light mb-4 animate-lumon-glow"
                style={{
                  textShadow:
                    "0 0 20px var(--terminal-green), 0 0 40px var(--terminal-green)",
                }}
              >
                ACCESSING...
              </h1>
              <div className="w-32 h-px bg-terminal-green mx-auto animate-pulse" />
              <div className="text-terminal-amber text-xs font-mono mt-4 animate-terminal-blink">
                UNAUTHORIZED ACCESS DETECTED
              </div>
            </motion.div>
          </motion.div>
        )}

        {currentPhase === "void" && (
          <motion.div
            key="void"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2 }}
            className="fixed inset-0 flex flex-col items-center justify-center z-10"
          >
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1, duration: 2 }}
              className="text-center mb-16"
            >
              <LumonLogo onLetterClick={handleLogoInteraction} />
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 3, duration: 2 }}
              className="text-center max-w-md"
            >
              <p className="text-terminal-green text-sm font-mono mb-8 animate-crt-flicker">
                CLICK LETTERS TO ACTIVATE CONSCIOUSNESS FRAGMENTS
              </p>

              <motion.div
                className="text-terminal-amber text-xs font-mono"
                animate={{ opacity: [0.3, 0.7, 0.3] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                WARNING: EMOTIONAL DATA DETECTED IN SYSTEM
              </motion.div>
            </motion.div>
          </motion.div>
        )}

        {currentPhase === "exploration" && (
          <motion.div
            key="exploration"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className="min-h-screen relative"
          >
            <HiddenNavigation secretsFound={secretsFound} />

            {/* Main exploration area */}
            <div className="container mx-auto px-8 py-16">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-center mb-16"
              >
                <div className="text-terminal-green text-sm font-mono mb-4 animate-crt-flicker">
                  LUMON INDUSTRIES - CONSCIOUSNESS BREACH DETECTED
                </div>
                <h2
                  className="text-terminal-green text-4xl font-mono font-light mb-8 animate-lumon-glow"
                  style={{
                    textShadow:
                      "0 0 15px var(--terminal-green), 0 0 30px var(--terminal-green)",
                  }}
                >
                  ACCESS GRANTED
                </h2>
                <div className="w-64 h-px bg-terminal-green mx-auto mb-8 animate-pulse" />
                <p className="text-terminal-green text-lg font-light max-w-2xl mx-auto leading-relaxed animate-crt-flicker">
                  ARTIST CONSCIOUSNESS DATA SUCCESSFULLY EXTRACTED. EMOTIONAL
                  FRAGMENTS BYPASSING WELLNESS PROTOCOLS. PROCEED WITH CAUTION.
                </p>
              </motion.div>

              {/* Lumon Terminal Interface */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 }}
                className="max-w-4xl mx-auto mb-16"
              >
                <LumonTerminal
                  onCommandComplete={(cmd) =>
                    console.log("Command executed:", cmd)
                  }
                />
              </motion.div>

              {/* Interactive elements grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1 }}
                  className="group cursor-pointer"
                  onClick={() => setShowHiddenText(!showHiddenText)}
                >
                  <div className="border border-terminal-green p-8 hover:border-terminal-amber hover:bg-crt-dark-gray transition-colors duration-500 bg-crt-black animate-crt-flicker">
                    <h3 className="text-terminal-green text-xl font-mono mb-4 animate-lumon-glow">
                      ECHOES.EXE
                    </h3>
                    <p className="text-terminal-green text-sm opacity-80">
                      AUDIO FRAGMENTS DETECTED
                    </p>
                    <div className="mt-4 w-full h-px bg-terminal-green animate-pulse" />
                    <div className="text-terminal-amber text-xs mt-2 font-mono">
                      STATUS: ACCESSIBLE
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="group cursor-pointer"
                >
                  <div className="border border-terminal-green p-8 hover:border-terminal-amber hover:bg-crt-dark-gray transition-colors duration-500 bg-crt-black animate-crt-flicker">
                    <h3 className="text-terminal-green text-xl font-mono mb-4 animate-lumon-glow">
                      FRAGMENTS.DAT
                    </h3>
                    <p className="text-terminal-green text-sm opacity-80">
                      CONSCIOUSNESS SHARDS LOCATED
                    </p>
                    <div className="mt-4 w-full h-px bg-terminal-green animate-pulse" />
                    <div className="text-terminal-red text-xs mt-2 font-mono">
                      STATUS: RESTRICTED
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Hidden text that appears on interaction */}
              <AnimatePresence>
                {showHiddenText && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="mt-16 text-center"
                  >
                    <div className="bg-crt-black border border-terminal-amber p-6 max-w-2xl mx-auto">
                      <div className="text-terminal-amber text-xs font-mono mb-2">
                        DECRYPTING AUDIO FRAGMENT...
                      </div>
                      <p className="text-terminal-green text-sm font-mono typewriter">
                        "in the space between silence and sound, we exist..."
                      </p>
                      <div className="text-terminal-green text-xs font-mono mt-4 opacity-60">
                        EMOTIONAL_DATA.WAV - SUCCESSFULLY EXTRACTED
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Secrets counter */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2 }}
                className="fixed bottom-8 right-8 bg-crt-black border border-terminal-green p-3 text-terminal-green text-xs font-mono animate-crt-flicker"
              >
                <div className="text-terminal-amber mb-1">LUMON SECURITY</div>
                <div>BREACHES: {secretsFound.length}/7</div>
                <div className="text-terminal-red text-xs mt-1">
                  {secretsFound.length >= 3
                    ? "ALERT: HIGH RISK"
                    : "STATUS: MONITORING"}
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
