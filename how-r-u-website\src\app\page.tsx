"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedLogo } from "@/components/AnimatedLogo";
import { EasterEggHunter } from "@/components/EasterEggHunter";
import { AmbientSoundscape } from "@/components/AmbientSoundscape";
import { HiddenNavigation } from "@/components/HiddenNavigation";

export default function Home() {
  const [currentPhase, setCurrentPhase] = useState<
    "entry" | "void" | "exploration"
  >("entry");
  const [secretsFound, setSecretsFound] = useState<string[]>([]);
  const [showHiddenText, setShowHiddenText] = useState(false);

  useEffect(() => {
    // Auto-transition from entry to void after 3 seconds
    const timer = setTimeout(() => {
      if (currentPhase === "entry") {
        setCurrentPhase("void");
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [currentPhase]);

  const handleLogoInteraction = (letter: string, index: number) => {
    if (letter === "secret") {
      setSecretsFound((prev) => [...prev, "logo_sequence"]);
      setCurrentPhase("exploration");
    }
  };

  const handleSecretFound = (secretId: string) => {
    setSecretsFound((prev) => [...prev, secretId]);
  };

  return (
    <div className="min-h-screen bg-void-700 relative overflow-hidden">
      <AmbientSoundscape />
      <EasterEggHunter onSecretFound={handleSecretFound} />
      <AnimatePresence mode="wait">
        {currentPhase === "entry" && (
          <motion.div
            key="entry"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2 }}
            className="fixed inset-0 flex items-center justify-center z-10"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, duration: 1.5 }}
              className="text-center"
            >
              <h1
                className="text-whisper-300 text-6xl font-mono font-light mb-4 glitch"
                data-text="entering"
              >
                entering
              </h1>
              <div className="w-32 h-px bg-gradient-to-r from-transparent via-whisper-200 to-transparent mx-auto" />
            </motion.div>
          </motion.div>
        )}

        {currentPhase === "void" && (
          <motion.div
            key="void"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2 }}
            className="fixed inset-0 flex flex-col items-center justify-center z-10"
          >
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1, duration: 2 }}
              className="text-center mb-16"
            >
              <AnimatedLogo onLetterClick={handleLogoInteraction} />
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 3, duration: 2 }}
              className="text-center max-w-md"
            >
              <p className="text-whisper-400 text-sm font-mono mb-8 breathing-text">
                click the letters... listen... feel...
              </p>

              <motion.div
                className="text-whisper-600 text-xs font-mono"
                animate={{ opacity: [0.3, 0.7, 0.3] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                there are secrets hidden in the darkness
              </motion.div>
            </motion.div>
          </motion.div>
        )}

        {currentPhase === "exploration" && (
          <motion.div
            key="exploration"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className="min-h-screen relative"
          >
            <HiddenNavigation secretsFound={secretsFound} />

            {/* Main exploration area */}
            <div className="container mx-auto px-8 py-16">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-center mb-16"
              >
                <h2
                  className="text-whisper-300 text-4xl font-mono font-light mb-8 glitch"
                  data-text="you found the way"
                >
                  you found the way
                </h2>
                <div className="w-64 h-px bg-gradient-to-r from-transparent via-whisper-200 to-transparent mx-auto mb-8" />
                <p className="text-whisper-500 text-lg font-light max-w-2xl mx-auto leading-relaxed">
                  welcome to the digital séance. here, consciousness fragments
                  drift through the void, waiting to be discovered. each
                  interaction reveals another layer of the mystery.
                </p>
              </motion.div>

              {/* Interactive elements grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1 }}
                  className="group cursor-pointer"
                  onClick={() => setShowHiddenText(!showHiddenText)}
                >
                  <div className="border border-whisper-100 p-8 hover:border-whisper-200 transition-colors duration-500 bg-ghost-50">
                    <h3 className="text-whisper-300 text-xl font-mono mb-4">
                      echoes
                    </h3>
                    <p className="text-whisper-500 text-sm">
                      fragments of sound and memory
                    </p>
                    <div className="mt-4 w-full h-px bg-gradient-to-r from-whisper-100 to-transparent" />
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="group cursor-pointer"
                >
                  <div className="border border-whisper-100 p-8 hover:border-whisper-200 transition-colors duration-500 bg-ghost-50">
                    <h3 className="text-whisper-300 text-xl font-mono mb-4">
                      fragments
                    </h3>
                    <p className="text-whisper-500 text-sm">
                      pieces of a larger consciousness
                    </p>
                    <div className="mt-4 w-full h-px bg-gradient-to-r from-whisper-100 to-transparent" />
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.4 }}
                  className="group cursor-pointer"
                >
                  <div className="border border-whisper-100 p-8 hover:border-whisper-200 transition-colors duration-500 bg-ghost-50">
                    <h3 className="text-whisper-300 text-xl font-mono mb-4">
                      contact
                    </h3>
                    <p className="text-whisper-500 text-sm">
                      reach through the veil
                    </p>
                    <div className="mt-4 w-full h-px bg-gradient-to-r from-whisper-100 to-transparent" />
                  </div>
                </motion.div>
              </div>

              {/* Hidden text that appears on interaction */}
              <AnimatePresence>
                {showHiddenText && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="mt-16 text-center"
                  >
                    <p className="text-whisper-400 text-sm font-mono typewriter">
                      "in the space between silence and sound, we exist..."
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Secrets counter */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2 }}
                className="fixed bottom-8 right-8 text-whisper-600 text-xs font-mono"
              >
                secrets found: {secretsFound.length}/7
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
