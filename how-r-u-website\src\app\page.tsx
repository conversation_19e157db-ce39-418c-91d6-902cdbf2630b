"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { DreamyLogo } from "@/components/DreamyLogo";
import { AnimatedSVGLogo } from "@/components/AnimatedSVGLogo";
import { Logo3D } from "@/components/Logo3D";
import { Scene3D } from "@/components/Scene3D";
import { SVG3DLogo } from "@/components/SVG3DLogo";
import { InteractiveGame } from "@/components/InteractiveGame";
import {
  ResponsiveLayout,
  AdaptiveControls,
  useResponsive,
} from "@/components/ResponsiveLayout";
import {
  EnhancedCard,
  EnhancedButton,
  GlowText,
  FloatingElement,
  ParticleField,
} from "@/components/EnhancedUI";
import { EasterEggHunter } from "@/components/EasterEggHunter";
import { AmbientSoundscape } from "@/components/AmbientSoundscape";
import { HiddenNavigation } from "@/components/HiddenNavigation";

export default function Home() {
  const [currentPhase, setCurrentPhase] = useState<
    "entry" | "void" | "exploration" | "game"
  >("entry");
  const [secretsFound, setSecretsFound] = useState<string[]>([]);
  const [showHiddenText, setShowHiddenText] = useState(false);
  const [logoMode, setLogoMode] = useState<
    "2d" | "svg" | "3d" | "scene" | "svg3d"
  >("svg3d");
  const [activeLetters, setActiveLetters] = useState<boolean[]>([
    false,
    false,
    false,
    false,
    false,
  ]);
  const [gameScore, setGameScore] = useState(0);

  const { isMobile, isTablet } = useResponsive();

  useEffect(() => {
    // Auto-transition from entry to void after 3 seconds
    const timer = setTimeout(() => {
      if (currentPhase === "entry") {
        setCurrentPhase("void");
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [currentPhase]);

  const handleLogoInteraction = (letter: string, index: number) => {
    if (letter === "secret") {
      setSecretsFound((prev) => [...prev, "logo_sequence"]);
      setCurrentPhase("exploration");
      // Animation spéciale pour toutes les lettres
      setActiveLetters([true, true, true, true, true]);
      setTimeout(
        () => setActiveLetters([false, false, false, false, false]),
        5000
      );
    } else {
      // Activer la lettre cliquée
      const newActiveLetters = [...activeLetters];
      newActiveLetters[index] = true;
      setActiveLetters(newActiveLetters);
      setTimeout(() => {
        const resetLetters = [...activeLetters];
        resetLetters[index] = false;
        setActiveLetters(resetLetters);
      }, 3000);
    }
  };

  const handleSecretFound = (secretId: string) => {
    setSecretsFound((prev) => [...prev, secretId]);
  };

  const handleGameComplete = (score: number) => {
    setGameScore(score);
    setSecretsFound((prev) => [...prev, "game_master"]);
    setCurrentPhase("exploration");
  };

  const handleGameLetterActivate = (index: number) => {
    const newActiveLetters = [...activeLetters];
    newActiveLetters[index] = true;
    setActiveLetters(newActiveLetters);
    setTimeout(() => {
      const resetLetters = [...activeLetters];
      resetLetters[index] = false;
      setActiveLetters(resetLetters);
    }, 500);
  };

  const logoModes = [
    { key: "svg3d", label: isMobile ? "SVG 3D" : "SVG 3D Extrudé" },
    { key: "svg", label: isMobile ? "SVG" : "SVG Animé" },
    { key: "2d", label: isMobile ? "2D" : "2D Dreamy" },
    { key: "3d", label: isMobile ? "3D" : "3D Texte" },
    { key: "scene", label: isMobile ? "Scène" : "Scène 3D" },
  ];

  return (
    <ResponsiveLayout className="bg-void-700">
      <AmbientSoundscape />
      <EasterEggHunter onSecretFound={handleSecretFound} />
      <AnimatePresence mode="wait">
        {currentPhase === "entry" && (
          <motion.div
            key="entry"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2 }}
            className="fixed inset-0 flex items-center justify-center z-10"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, duration: 1.5 }}
              className="text-center"
            >
              <motion.div
                className="text-whisper-400 text-sm font-mono mb-8 animate-whisper-glow"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                entering the void...
              </motion.div>
              <h1
                className="text-whisper-300 text-6xl font-light mb-4 animate-dreamy-float"
                style={{
                  textShadow: "0 0 30px var(--whisper-bright)",
                }}
              >
                how r u
              </h1>
              <div className="w-32 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto animate-gentle-sway" />
              <motion.div
                className="text-whisper-500 text-xs font-light mt-4"
                animate={{ opacity: [0.3, 0.8, 0.3] }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                consciousness loading...
              </motion.div>
            </motion.div>
          </motion.div>
        )}

        {currentPhase === "void" && (
          <motion.div
            key="void"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2 }}
            className="fixed inset-0 flex flex-col items-center justify-center z-10"
          >
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 1, duration: 2 }}
              className="text-center mb-16"
            >
              {logoMode === "svg3d" && (
                <SVG3DLogo
                  onLetterClick={handleLogoInteraction}
                  activeLetters={activeLetters}
                  className={isMobile ? "h-[400px]" : "h-[600px]"}
                />
              )}
              {logoMode === "2d" && (
                <DreamyLogo onLetterClick={handleLogoInteraction} />
              )}
              {logoMode === "svg" && (
                <AnimatedSVGLogo onLetterClick={handleLogoInteraction} />
              )}
              {logoMode === "3d" && (
                <Logo3D onLetterClick={handleLogoInteraction} />
              )}
              {logoMode === "scene" && (
                <Scene3D activeLetters={activeLetters} />
              )}
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 3, duration: 2 }}
              className="text-center max-w-md"
            >
              <p className="text-whisper-400 text-sm font-light mb-8 animate-whisper-glow">
                click the letters... listen... feel...
              </p>

              <motion.div
                className="text-whisper-600 text-xs font-light"
                animate={{ opacity: [0.3, 0.7, 0.3] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                there are secrets hidden in the darkness
              </motion.div>
            </motion.div>

            {/* Contrôles adaptatifs */}
            <AdaptiveControls
              onModeChange={(mode) => setLogoMode(mode as any)}
              currentMode={logoMode}
              modes={logoModes}
            />
          </motion.div>
        )}

        {currentPhase === "exploration" && (
          <motion.div
            key="exploration"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className="min-h-screen relative"
          >
            <HiddenNavigation secretsFound={secretsFound} />

            {/* Main exploration area */}
            <div className="container mx-auto px-8 py-16">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-center mb-16"
              >
                <motion.div
                  className="text-whisper-500 text-sm font-light mb-4 animate-whisper-glow"
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 4, repeat: Infinity }}
                >
                  consciousness fragments detected...
                </motion.div>
                <h2
                  className="text-whisper-300 text-4xl font-light mb-8 animate-dreamy-float"
                  style={{
                    textShadow: "0 0 25px var(--whisper-bright)",
                  }}
                >
                  you found the way
                </h2>
                <div className="w-64 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto mb-8 animate-gentle-sway" />
                <p className="text-whisper-400 text-lg font-light max-w-2xl mx-auto leading-relaxed">
                  welcome to the digital séance. here, consciousness fragments
                  drift through the void, waiting to be discovered. each
                  interaction reveals another layer of the mystery.
                </p>
              </motion.div>

              {/* Interactive elements grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1 }}
                  className="group cursor-pointer"
                  onClick={() => setShowHiddenText(!showHiddenText)}
                >
                  <div className="border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-dreamy-float">
                    <h3 className="text-whisper-300 text-xl font-light mb-4 animate-whisper-glow">
                      echoes
                    </h3>
                    <p className="text-whisper-500 text-sm opacity-80">
                      fragments of sound and memory
                    </p>
                    <div className="mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-gentle-sway" />
                    <div className="text-whisper-600 text-xs mt-2 font-light">
                      drift through silence...
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="group cursor-pointer"
                  onClick={() => setCurrentPhase("game")}
                >
                  <div className="border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-gentle-sway">
                    <h3 className="text-whisper-300 text-xl font-light mb-4 animate-whisper-glow">
                      jeu de mémoire
                    </h3>
                    <p className="text-whisper-500 text-sm opacity-80">
                      teste ta conscience avec les lettres
                    </p>
                    <div className="mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-dreamy-float" />
                    <div className="text-whisper-600 text-xs mt-2 font-light">
                      {gameScore > 0
                        ? `meilleur score: ${gameScore}`
                        : "clique pour jouer..."}
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Hidden text that appears on interaction */}
              <AnimatePresence>
                {showHiddenText && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="mt-16 text-center"
                  >
                    <div className="bg-ghost-100 border border-whisper-300 p-6 max-w-2xl mx-auto backdrop-blur-md animate-whisper-glow">
                      <div className="text-whisper-400 text-xs font-light mb-2 animate-dreamy-float">
                        revealing hidden memory...
                      </div>
                      <p className="text-whisper-300 text-sm font-light typewriter">
                        "in the space between silence and sound, we exist..."
                      </p>
                      <div className="text-whisper-500 text-xs font-light mt-4 opacity-60">
                        fragment recovered from the void
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Secrets counter */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2 }}
                className="fixed bottom-8 right-8 bg-ghost-100 border border-whisper-200 p-3 text-whisper-400 text-xs font-light backdrop-blur-sm animate-whisper-glow"
              >
                <div className="text-whisper-300 mb-1">secrets found</div>
                <div>{secretsFound.length}/7</div>
                <div className="text-whisper-500 text-xs mt-1">
                  {secretsFound.length >= 3
                    ? "consciousness awakening..."
                    : "searching the void..."}
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}

        {currentPhase === "game" && (
          <motion.div
            key="game"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1 }}
            className="min-h-screen relative flex items-center justify-center"
          >
            <div className="fixed top-4 left-4 z-30">
              <EnhancedButton
                onClick={() => setCurrentPhase("exploration")}
                variant="ghost"
                size="sm"
              >
                ← Retour
              </EnhancedButton>
            </div>

            <InteractiveGame
              onGameComplete={handleGameComplete}
              onLetterActivate={handleGameLetterActivate}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </ResponsiveLayout>
  );
}
