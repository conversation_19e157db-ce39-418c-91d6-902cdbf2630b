'use client';

import { useState, useEffect, ReactNode } from 'react';
import { motion } from 'framer-motion';

interface ResponsiveLayoutProps {
  children: ReactNode;
  className?: string;
}

export function ResponsiveLayout({ children, className = "" }: ResponsiveLayoutProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
      setOrientation(height > width ? 'portrait' : 'landscape');
    };
    
    checkDevice();
    window.addEventListener('resize', checkDevice);
    window.addEventListener('orientationchange', () => {
      setTimeout(checkDevice, 100); // <PERSON><PERSON><PERSON> pour laisser le temps à l'orientation de changer
    });
    
    return () => {
      window.removeEventListener('resize', checkDevice);
      window.removeEventListener('orientationchange', checkDevice);
    };
  }, []);

  const getLayoutClasses = () => {
    let classes = "min-h-screen w-full relative overflow-hidden ";
    
    if (isMobile) {
      classes += orientation === 'portrait' 
        ? "px-4 py-6 " 
        : "px-6 py-4 ";
    } else if (isTablet) {
      classes += "px-8 py-8 ";
    } else {
      classes += "px-12 py-12 ";
    }
    
    return classes + className;
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  return (
    <motion.div
      className={getLayoutClasses()}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Indicateur de device (dev only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-2 right-2 z-50 text-xs bg-black/50 text-white px-2 py-1 rounded">
          {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'} - {orientation}
        </div>
      )}
      
      <motion.div variants={itemVariants}>
        {children}
      </motion.div>
    </motion.div>
  );
}

// Hook personnalisé pour la responsivité
export function useResponsive() {
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    orientation: 'portrait' as 'portrait' | 'landscape',
    width: 0,
    height: 0
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setDeviceInfo({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        orientation: height > width ? 'portrait' : 'landscape',
        width,
        height
      });
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', () => {
      setTimeout(updateDeviceInfo, 100);
    });

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, []);

  return deviceInfo;
}

// Composant pour les contrôles adaptatifs
interface AdaptiveControlsProps {
  onModeChange: (mode: string) => void;
  currentMode: string;
  modes: { key: string; label: string }[];
}

export function AdaptiveControls({ onModeChange, currentMode, modes }: AdaptiveControlsProps) {
  const { isMobile, isTablet } = useResponsive();

  if (isMobile) {
    // Version mobile : menu déroulant
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed top-4 left-4 right-4 z-20"
      >
        <select
          value={currentMode}
          onChange={(e) => onModeChange(e.target.value)}
          className="w-full bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light p-2 rounded"
        >
          {modes.map((mode) => (
            <option key={mode.key} value={mode.key}>
              {mode.label}
            </option>
          ))}
        </select>
      </motion.div>
    );
  }

  if (isTablet) {
    // Version tablette : boutons horizontaux
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed top-4 left-4 right-4 z-20"
      >
        <div className="flex space-x-2 justify-center">
          {modes.map((mode) => (
            <button
              key={mode.key}
              onClick={() => onModeChange(mode.key)}
              className={`px-3 py-1 text-xs font-light transition-colors duration-300 rounded ${
                currentMode === mode.key
                  ? 'text-whisper-300 bg-ghost-200 border border-whisper-300'
                  : 'text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400'
              }`}
            >
              {mode.label}
            </button>
          ))}
        </div>
      </motion.div>
    );
  }

  // Version desktop : menu vertical
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="fixed top-8 left-8 space-y-2 z-20"
    >
      <div className="text-whisper-500 text-xs font-light mb-2">
        mode d'affichage:
      </div>
      {modes.map((mode) => (
        <button
          key={mode.key}
          onClick={() => onModeChange(mode.key)}
          className={`block w-full text-left px-3 py-1 text-xs font-light transition-colors duration-300 ${
            currentMode === mode.key
              ? 'text-whisper-300 bg-ghost-200 border border-whisper-300'
              : 'text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400 hover:bg-ghost-150'
          }`}
        >
          {mode.label}
        </button>
      ))}
    </motion.div>
  );
}
