'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('../../dist/classCallCheck-eaf0efc7.cjs.dev.js');
var geometry_dist_maathGeometry = require('../../dist/geometry-a03c19ff.cjs.dev.js');
require('three');
require('../../dist/isNativeReflectConstruct-ddc4ebc1.cjs.dev.js');



exports.RoundedPlaneGeometry = geometry_dist_maathGeometry.RoundedPlaneGeometry;
exports.applyBoxUV = geometry_dist_maathGeometry.applyBoxUV;
exports.applySphereUV = geometry_dist_maathGeometry.applySphereUV;
