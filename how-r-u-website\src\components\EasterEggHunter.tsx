'use client';

import { useEffect, useState } from 'react';

interface EasterEggHunterProps {
  onSecretFound: (secretId: string) => void;
}

export function EasterEggHunter({ onSecretFound }: EasterEggHunterProps) {
  const [konamiSequence, setKonamiSequence] = useState<string[]>([]);
  const [clickCount, setClickCount] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);

  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const newSequence = [...konamiSequence, e.code];
      
      // Keep only the last 10 keys
      if (newSequence.length > 10) {
        newSequence.shift();
      }
      
      setKonamiSequence(newSequence);
      
      // Check if konami code is complete
      if (newSequence.length === 10 && 
          newSequence.every((key, index) => key === konamiCode[index])) {
        onSecretFound('konami_code');
        setKonamiSequence([]);
      }
    };

    const handleClick = (e: MouseEvent) => {
      const now = Date.now();
      
      // Reset if too much time has passed
      if (now - lastClickTime > 1000) {
        setClickCount(1);
      } else {
        setClickCount(prev => prev + 1);
      }
      
      setLastClickTime(now);
      
      // Secret: 7 rapid clicks
      if (clickCount >= 7) {
        onSecretFound('rapid_clicks');
        setClickCount(0);
      }
      
      // Secret: clicking in corners
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;
      
      if ((clientX < 50 && clientY < 50) || 
          (clientX > innerWidth - 50 && clientY < 50) ||
          (clientX < 50 && clientY > innerHeight - 50) ||
          (clientX > innerWidth - 50 && clientY > innerHeight - 50)) {
        onSecretFound('corner_click');
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      // Secret: mouse idle in center for 5 seconds
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;
      
      if (Math.abs(clientX - innerWidth / 2) < 20 && 
          Math.abs(clientY - innerHeight / 2) < 20) {
        setTimeout(() => {
          onSecretFound('center_idle');
        }, 5000);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClick);
    document.addEventListener('mousemove', handleMouseMove);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClick);
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [konamiSequence, clickCount, lastClickTime, onSecretFound]);

  return null; // This component is invisible
}
