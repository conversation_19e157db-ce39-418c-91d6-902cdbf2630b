{"version": 3, "file": "TextGeometry.cjs", "sources": ["../../src/geometries/TextGeometry.ts"], "sourcesContent": ["import { ExtrudeGeometry } from 'three'\n\nimport type { Font } from '../loaders/FontLoader'\n\nexport type TextGeometryParameters = {\n  bevelEnabled?: boolean\n  bevelOffset?: number\n  bevelSize?: number\n  bevelThickness?: number\n  curveSegments?: number\n  font: Font\n  height?: number\n  size?: number\n  lineHeight?: number\n  letterSpacing?: number\n}\n\nexport class TextGeometry extends ExtrudeGeometry {\n  constructor(text: string, parameters: TextGeometryParameters = {} as TextGeometryParameters) {\n    const {\n      bevelEnabled = false,\n      bevelSize = 8,\n      bevelThickness = 10,\n      font,\n      height = 50,\n      size = 100,\n      lineHeight = 1,\n      letterSpacing = 0,\n      ...rest\n    } = parameters\n\n    if (font === undefined) {\n      // @ts-ignore\n      super() // generate default extrude geometry\n    } else {\n      const shapes = font.generateShapes(text, size, { lineHeight, letterSpacing })\n      super(shapes, { ...rest, bevelEnabled, bevelSize, bevelThickness, depth: height })\n    }\n    // @ts-ignore\n    this.type = 'TextGeometry'\n  }\n}\n\nexport { TextGeometry as TextBufferGeometry }\n"], "names": ["ExtrudeGeometry"], "mappings": ";;;AAiBO,MAAM,qBAAqBA,MAAAA,gBAAgB;AAAA,EAChD,YAAY,MAAc,aAAqC,IAA8B;AACrF,UAAA;AAAA,MACJ,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACD,IAAA;AAEJ,QAAI,SAAS,QAAW;AAEhB;IAAA,OACD;AACC,YAAA,SAAS,KAAK,eAAe,MAAM,MAAM,EAAE,YAAY,eAAe;AACtE,YAAA,QAAQ,EAAE,GAAG,MAAM,cAAc,WAAW,gBAAgB,OAAO,OAAA,CAAQ;AAAA,IACnF;AAEA,SAAK,OAAO;AAAA,EACd;AACF;;;"}