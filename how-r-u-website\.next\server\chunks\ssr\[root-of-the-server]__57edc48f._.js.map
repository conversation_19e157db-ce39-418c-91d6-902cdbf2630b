{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/CustomCursor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport function CustomCursor() {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const updatePosition = (e: MouseEvent) => {\n      setPosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseEnter = () => setIsVisible(true);\n    const handleMouseLeave = () => setIsVisible(false);\n\n    document.addEventListener('mousemove', updatePosition);\n    document.addEventListener('mouseenter', handleMouseEnter);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    return () => {\n      document.removeEventListener('mousemove', updatePosition);\n      document.removeEventListener('mouseenter', handleMouseEnter);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n    };\n  }, []);\n\n  return (\n    <div\n      className={`custom-cursor ${isVisible ? 'opacity-100' : 'opacity-0'}`}\n      style={{\n        left: position.x - 10,\n        top: position.y - 10,\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,CAAC;YACtB,YAAY;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAC3C;QAEA,MAAM,mBAAmB,IAAM,aAAa;QAC5C,MAAM,mBAAmB,IAAM,aAAa;QAE5C,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,cAAc;QACxC,SAAS,gBAAgB,CAAC,cAAc;QAExC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,cAAc;YAC3C,SAAS,mBAAmB,CAAC,cAAc;QAC7C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,cAAc,EAAE,YAAY,gBAAgB,aAAa;QACrE,OAAO;YACL,MAAM,SAAS,CAAC,GAAG;YACnB,KAAK,SAAS,CAAC,GAAG;QACpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/DustParticles.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\n\ninterface Particle {\n  id: number;\n  x: number;\n  y: number;\n  delay: number;\n  size: number;\n  opacity: number;\n  speed: number;\n}\n\nexport function DustParticles() {\n  const [particles, setParticles] = useState<Particle[]>([]);\n\n  useEffect(() => {\n    const generateParticles = () => {\n      const newParticles: Particle[] = [];\n      for (let i = 0; i < 30; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * window.innerWidth,\n          y: window.innerHeight + Math.random() * 100,\n          delay: Math.random() * 25,\n          size: Math.random() * 4 + 1,\n          opacity: Math.random() * 0.3 + 0.1,\n          speed: Math.random() * 15 + 10,\n        });\n      }\n      setParticles(newParticles);\n    };\n\n    generateParticles();\n    window.addEventListener(\"resize\", generateParticles);\n\n    return () => window.removeEventListener(\"resize\", generateParticles);\n  }, []);\n\n  return (\n    <div className=\"fixed inset-0 pointer-events-none z-0\">\n      {particles.map((particle) => (\n        <div\n          key={particle.id}\n          className=\"absolute rounded-full bg-white animate-ethereal-drift\"\n          style={{\n            left: particle.x,\n            bottom: 0,\n            animationDelay: `${particle.delay}s`,\n            animationDuration: `${particle.speed}s`,\n            width: particle.size,\n            height: particle.size,\n            opacity: particle.opacity,\n            boxShadow: `0 0 ${particle.size * 2}px rgba(255, 255, 255, ${\n              particle.opacity\n            })`,\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,MAAM,eAA2B,EAAE;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,aAAa,IAAI,CAAC;oBAChB,IAAI;oBACJ,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;oBACpC,GAAG,OAAO,WAAW,GAAG,KAAK,MAAM,KAAK;oBACxC,OAAO,KAAK,MAAM,KAAK;oBACvB,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;oBAC/B,OAAO,KAAK,MAAM,KAAK,KAAK;gBAC9B;YACF;YACA,aAAa;QACf;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,MAAM,SAAS,CAAC;oBAChB,QAAQ;oBACR,gBAAgB,GAAG,SAAS,KAAK,CAAC,CAAC,CAAC;oBACpC,mBAAmB,GAAG,SAAS,KAAK,CAAC,CAAC,CAAC;oBACvC,OAAO,SAAS,IAAI;oBACpB,QAAQ,SAAS,IAAI;oBACrB,SAAS,SAAS,OAAO;oBACzB,WAAW,CAAC,IAAI,EAAE,SAAS,IAAI,GAAG,EAAE,uBAAuB,EACzD,SAAS,OAAO,CACjB,CAAC,CAAC;gBACL;eAbK,SAAS,EAAE;;;;;;;;;;AAkB1B", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}