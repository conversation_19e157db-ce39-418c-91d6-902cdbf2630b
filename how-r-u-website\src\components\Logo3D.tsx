'use client';

import { useRef, useState, useEffect } from 'react';
import { <PERSON><PERSON>, useFrame, useThree } from '@react-three/fiber';
import { Text3D, Center, Float, MeshDistortMaterial, Sphere, Environment, Effects } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';

interface Letter3DProps {
  letter: string;
  position: [number, number, number];
  index: number;
  isActive: boolean;
  onClick: () => void;
}

function Letter3D({ letter, position, index, isActive, onClick }: Letter3DProps) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  
  useFrame((state) => {
    if (meshRef.current) {
      // Animation de base flottante
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.3;
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;
      meshRef.current.rotation.z = Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;
      
      // Animation quand actif
      if (isActive) {
        meshRef.current.rotation.y += 0.02;
        meshRef.current.scale.setScalar(1.2 + Math.sin(state.clock.elapsedTime * 3) * 0.1);
      } else {
        meshRef.current.scale.setScalar(hovered ? 1.1 : 1);
      }
    }
  });

  return (
    <Float
      speed={2}
      rotationIntensity={0.5}
      floatIntensity={0.5}
    >
      <Text3D
        ref={meshRef}
        font="/fonts/helvetiker_regular.typeface.json"
        size={1.5}
        height={0.3}
        position={position}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        onClick={onClick}
        curveSegments={12}
      >
        {letter}
        <MeshDistortMaterial
          color={isActive ? "#ffffff" : hovered ? "#e0e0e0" : "#a0a0a0"}
          distort={isActive ? 0.4 : hovered ? 0.2 : 0.1}
          speed={isActive ? 3 : 1}
          roughness={0.1}
          metalness={0.8}
        />
      </Text3D>
    </Float>
  );
}

function ParticleField() {
  const pointsRef = useRef<THREE.Points>(null);
  const particleCount = 200;
  
  const positions = new Float32Array(particleCount * 3);
  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 20;
    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
  }

  useFrame((state) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;
      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;
    }
  });

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.02}
        color="#ffffff"
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
}

function BackgroundSphere() {
  const sphereRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (sphereRef.current) {
      sphereRef.current.rotation.y = state.clock.elapsedTime * 0.01;
      sphereRef.current.rotation.x = state.clock.elapsedTime * 0.005;
    }
  });

  return (
    <Sphere ref={sphereRef} args={[15, 32, 32]} position={[0, 0, -10]}>
      <MeshDistortMaterial
        color="#111111"
        distort={0.3}
        speed={1}
        roughness={0.8}
        metalness={0.2}
        transparent
        opacity={0.3}
      />
    </Sphere>
  );
}

function CameraController() {
  const { camera } = useThree();
  
  useFrame((state) => {
    camera.position.x = Math.sin(state.clock.elapsedTime * 0.1) * 2;
    camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 1;
    camera.lookAt(0, 0, 0);
  });
  
  return null;
}

interface Logo3DProps {
  onLetterClick?: (letter: string, index: number) => void;
}

export function Logo3D({ onLetterClick }: Logo3DProps) {
  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);
  const [clickSequence, setClickSequence] = useState<number[]>([]);
  
  const letters = ['h', 'o', 'w', 'r', 'u'];
  const positions: [number, number, number][] = [
    [-3, 0, 0],
    [-1.5, 0, 0],
    [0, 0, 0],
    [1.5, 0, 0],
    [3, 0, 0]
  ];
  
  const secretSequence = [0, 4, 1, 3, 2]; // h-u-o-r-w

  const handleLetterClick = (letter: string, index: number) => {
    const newSequence = [...clickSequence, index];
    setClickSequence(newSequence);

    // Activer la lettre avec animation
    const newActiveLetters = [...activeLetters];
    newActiveLetters[index] = true;
    setActiveLetters(newActiveLetters);

    // Reset après 3 secondes
    setTimeout(() => {
      const resetLetters = [...activeLetters];
      resetLetters[index] = false;
      setActiveLetters(resetLetters);
    }, 3000);

    // Vérifier la séquence secrète
    if (newSequence.length === secretSequence.length) {
      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);
      if (isCorrect) {
        onLetterClick?.('secret', -1);
        // Animation spéciale pour toutes les lettres
        setActiveLetters([true, true, true, true, true]);
        setTimeout(() => setActiveLetters([false, false, false, false, false]), 5000);
      }
      setTimeout(() => setClickSequence([]), 1000);
    }

    onLetterClick?.(letter, index);
  };

  return (
    <div className="w-full h-[600px] relative">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 2, ease: "easeOut" }}
        className="w-full h-full"
      >
        <Canvas
          camera={{ position: [0, 0, 8], fov: 50 }}
          gl={{ antialias: true, alpha: true }}
          style={{ background: 'transparent' }}
        >
          <ambientLight intensity={0.3} />
          <pointLight position={[10, 10, 10]} intensity={1} />
          <pointLight position={[-10, -10, -10]} intensity={0.5} color="#4a5568" />
          
          <Environment preset="night" />
          
          <BackgroundSphere />
          <ParticleField />
          
          <Center>
            {letters.map((letter, index) => (
              <Letter3D
                key={letter + index}
                letter={letter}
                position={positions[index]}
                index={index}
                isActive={activeLetters[index]}
                onClick={() => handleLetterClick(letter, index)}
              />
            ))}
          </Center>
          
          <CameraController />
        </Canvas>
      </motion.div>
      
      {/* Instructions flottantes */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center"
      >
        <p className="text-whisper-400 text-sm font-light mb-2">
          cliquez sur les lettres pour les animer
        </p>
        <p className="text-whisper-600 text-xs font-light">
          trouvez la séquence secrète...
        </p>
      </motion.div>
    </div>
  );
}
