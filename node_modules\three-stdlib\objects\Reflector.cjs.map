{"version": 3, "file": "Reflector.cjs", "sources": ["../../src/objects/Reflector.js"], "sourcesContent": ["import {\n  Color,\n  Matrix4,\n  Mesh,\n  PerspectiveCamera,\n  Plane,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector3,\n  Vector4,\n  WebGLRenderTarget,\n  HalfFloatType,\n  NoToneMapping,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nconst Reflector = /* @__PURE__ */ (() => {\n  class Reflector extends Mesh {\n    static ReflectorShader = {\n      uniforms: {\n        color: {\n          value: null,\n        },\n\n        tDiffuse: {\n          value: null,\n        },\n\n        textureMatrix: {\n          value: null,\n        },\n      },\n\n      vertexShader: /* glsl */ `\n\t\tuniform mat4 textureMatrix;\n\t\tvarying vec4 vUv;\n\n\t\t#include <common>\n\t\t#include <logdepthbuf_pars_vertex>\n\n\t\tvoid main() {\n\n\t\t\tvUv = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t\t#include <logdepthbuf_vertex>\n\n\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\t\tuniform vec3 color;\n\t\tuniform sampler2D tDiffuse;\n\t\tvarying vec4 vUv;\n\n\t\t#include <logdepthbuf_pars_fragment>\n\n\t\tfloat blendOverlay( float base, float blend ) {\n\n\t\t\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );\n\n\t\t}\n\n\t\tvec3 blendOverlay( vec3 base, vec3 blend ) {\n\n\t\t\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\tvec4 base = texture2DProj( tDiffuse, vUv );\n\t\t\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\n\t\t}`,\n    }\n\n    constructor(geometry, options = {}) {\n      super(geometry)\n\n      this.isReflector = true\n\n      this.type = 'Reflector'\n      this.camera = new PerspectiveCamera()\n\n      const scope = this\n\n      const color = options.color !== undefined ? new Color(options.color) : new Color(0x7f7f7f)\n      const textureWidth = options.textureWidth || 512\n      const textureHeight = options.textureHeight || 512\n      const clipBias = options.clipBias || 0\n      const shader = options.shader || Reflector.ReflectorShader\n      const multisample = options.multisample !== undefined ? options.multisample : 4\n\n      //\n\n      const reflectorPlane = new Plane()\n      const normal = new Vector3()\n      const reflectorWorldPosition = new Vector3()\n      const cameraWorldPosition = new Vector3()\n      const rotationMatrix = new Matrix4()\n      const lookAtPosition = new Vector3(0, 0, -1)\n      const clipPlane = new Vector4()\n\n      const view = new Vector3()\n      const target = new Vector3()\n      const q = new Vector4()\n\n      const textureMatrix = new Matrix4()\n      const virtualCamera = this.camera\n\n      const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight, {\n        samples: multisample,\n        type: HalfFloatType,\n      })\n\n      const material = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(shader.uniforms),\n        fragmentShader: shader.fragmentShader,\n        vertexShader: shader.vertexShader,\n      })\n\n      material.uniforms['tDiffuse'].value = renderTarget.texture\n      material.uniforms['color'].value = color\n      material.uniforms['textureMatrix'].value = textureMatrix\n\n      this.material = material\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        reflectorWorldPosition.setFromMatrixPosition(scope.matrixWorld)\n        cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld)\n\n        rotationMatrix.extractRotation(scope.matrixWorld)\n\n        normal.set(0, 0, 1)\n        normal.applyMatrix4(rotationMatrix)\n\n        view.subVectors(reflectorWorldPosition, cameraWorldPosition)\n\n        // Avoid rendering when reflector is facing away\n\n        if (view.dot(normal) > 0) return\n\n        view.reflect(normal).negate()\n        view.add(reflectorWorldPosition)\n\n        rotationMatrix.extractRotation(camera.matrixWorld)\n\n        lookAtPosition.set(0, 0, -1)\n        lookAtPosition.applyMatrix4(rotationMatrix)\n        lookAtPosition.add(cameraWorldPosition)\n\n        target.subVectors(reflectorWorldPosition, lookAtPosition)\n        target.reflect(normal).negate()\n        target.add(reflectorWorldPosition)\n\n        virtualCamera.position.copy(view)\n        virtualCamera.up.set(0, 1, 0)\n        virtualCamera.up.applyMatrix4(rotationMatrix)\n        virtualCamera.up.reflect(normal)\n        virtualCamera.lookAt(target)\n\n        virtualCamera.far = camera.far // Used in WebGLBackground\n\n        virtualCamera.updateMatrixWorld()\n        virtualCamera.projectionMatrix.copy(camera.projectionMatrix)\n\n        // Update the texture matrix\n        textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n        textureMatrix.multiply(virtualCamera.projectionMatrix)\n        textureMatrix.multiply(virtualCamera.matrixWorldInverse)\n        textureMatrix.multiply(scope.matrixWorld)\n\n        // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n        // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n        reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition)\n        reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse)\n\n        clipPlane.set(\n          reflectorPlane.normal.x,\n          reflectorPlane.normal.y,\n          reflectorPlane.normal.z,\n          reflectorPlane.constant,\n        )\n\n        const projectionMatrix = virtualCamera.projectionMatrix\n\n        q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0]\n        q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5]\n        q.z = -1.0\n        q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]\n\n        // Calculate the scaled plane vector\n        clipPlane.multiplyScalar(2.0 / clipPlane.dot(q))\n\n        // Replacing the third row of the projection matrix\n        projectionMatrix.elements[2] = clipPlane.x\n        projectionMatrix.elements[6] = clipPlane.y\n        projectionMatrix.elements[10] = clipPlane.z + 1.0 - clipBias\n        projectionMatrix.elements[14] = clipPlane.w\n\n        // Render\n        scope.visible = false\n\n        const currentRenderTarget = renderer.getRenderTarget()\n\n        const currentXrEnabled = renderer.xr.enabled\n        const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n        const currentToneMapping = renderer.toneMapping\n\n        let isSRGB = false\n        if ('outputColorSpace' in renderer) isSRGB = renderer.outputColorSpace === 'srgb'\n        else isSRGB = renderer.outputEncoding === 3001 // sRGBEncoding\n\n        renderer.xr.enabled = false // Avoid camera modification\n        renderer.shadowMap.autoUpdate = false // Avoid re-computing shadows\n        if ('outputColorSpace' in renderer) renderer.outputColorSpace = 'srgb-linear'\n        else renderer.outputEncoding = 3000 // LinearEncoding\n        renderer.toneMapping = NoToneMapping\n\n        renderer.setRenderTarget(renderTarget)\n\n        renderer.state.buffers.depth.setMask(true) // make sure the depth buffer is writable so it can be properly cleared, see #18897\n\n        if (renderer.autoClear === false) renderer.clear()\n        renderer.render(scene, virtualCamera)\n\n        renderer.xr.enabled = currentXrEnabled\n        renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n        renderer.toneMapping = currentToneMapping\n\n        if ('outputColorSpace' in renderer) renderer.outputColorSpace = isSRGB ? 'srgb' : 'srgb-linear'\n        else renderer.outputEncoding = isSRGB ? 3001 : 3000\n\n        renderer.setRenderTarget(currentRenderTarget)\n\n        // Restore viewport\n\n        const viewport = camera.viewport\n\n        if (viewport !== undefined) {\n          renderer.state.viewport(viewport)\n        }\n\n        scope.visible = true\n      }\n\n      this.getRenderTarget = function () {\n        return renderTarget\n      }\n\n      this.dispose = function () {\n        renderTarget.dispose()\n        scope.material.dispose()\n      }\n    }\n  }\n\n  return Reflector\n})()\n\nexport { Reflector }\n"], "names": ["<PERSON><PERSON>", "PerspectiveCamera", "Color", "Plane", "Vector3", "Matrix4", "Vector4", "WebGLRenderTarget", "HalfFloatType", "ShaderMaterial", "UniformsUtils", "NoToneMapping", "Reflector", "version"], "mappings": ";;;;;;;;;;AAgBK,MAAC,YAA6B,uBAAM;AACvC,QAAM,aAAN,cAAwBA,MAAAA,KAAK;AAAA,IAiE3B,YAAY,UAAU,UAAU,IAAI;AAClC,YAAM,QAAQ;AAEd,WAAK,cAAc;AAEnB,WAAK,OAAO;AACZ,WAAK,SAAS,IAAIC,wBAAmB;AAErC,YAAM,QAAQ;AAEd,YAAM,QAAQ,QAAQ,UAAU,SAAY,IAAIC,YAAM,QAAQ,KAAK,IAAI,IAAIA,MAAAA,MAAM,OAAQ;AACzF,YAAM,eAAe,QAAQ,gBAAgB;AAC7C,YAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,YAAM,WAAW,QAAQ,YAAY;AACrC,YAAM,SAAS,QAAQ,UAAU,WAAU;AAC3C,YAAM,cAAc,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAI9E,YAAM,iBAAiB,IAAIC,YAAO;AAClC,YAAM,SAAS,IAAIC,cAAS;AAC5B,YAAM,yBAAyB,IAAIA,cAAS;AAC5C,YAAM,sBAAsB,IAAIA,cAAS;AACzC,YAAM,iBAAiB,IAAIC,cAAS;AACpC,YAAM,iBAAiB,IAAID,MAAO,QAAC,GAAG,GAAG,EAAE;AAC3C,YAAM,YAAY,IAAIE,cAAS;AAE/B,YAAM,OAAO,IAAIF,cAAS;AAC1B,YAAM,SAAS,IAAIA,cAAS;AAC5B,YAAM,IAAI,IAAIE,cAAS;AAEvB,YAAM,gBAAgB,IAAID,cAAS;AACnC,YAAM,gBAAgB,KAAK;AAE3B,YAAM,eAAe,IAAIE,wBAAkB,cAAc,eAAe;AAAA,QACtE,SAAS;AAAA,QACT,MAAMC,MAAa;AAAA,MAC3B,CAAO;AAED,YAAM,WAAW,IAAIC,qBAAe;AAAA,QAClC,UAAUC,MAAa,cAAC,MAAM,OAAO,QAAQ;AAAA,QAC7C,gBAAgB,OAAO;AAAA,QACvB,cAAc,OAAO;AAAA,MAC7B,CAAO;AAED,eAAS,SAAS,UAAU,EAAE,QAAQ,aAAa;AACnD,eAAS,SAAS,OAAO,EAAE,QAAQ;AACnC,eAAS,SAAS,eAAe,EAAE,QAAQ;AAE3C,WAAK,WAAW;AAEhB,WAAK,iBAAiB,SAAU,UAAU,OAAO,QAAQ;AACvD,+BAAuB,sBAAsB,MAAM,WAAW;AAC9D,4BAAoB,sBAAsB,OAAO,WAAW;AAE5D,uBAAe,gBAAgB,MAAM,WAAW;AAEhD,eAAO,IAAI,GAAG,GAAG,CAAC;AAClB,eAAO,aAAa,cAAc;AAElC,aAAK,WAAW,wBAAwB,mBAAmB;AAI3D,YAAI,KAAK,IAAI,MAAM,IAAI;AAAG;AAE1B,aAAK,QAAQ,MAAM,EAAE,OAAQ;AAC7B,aAAK,IAAI,sBAAsB;AAE/B,uBAAe,gBAAgB,OAAO,WAAW;AAEjD,uBAAe,IAAI,GAAG,GAAG,EAAE;AAC3B,uBAAe,aAAa,cAAc;AAC1C,uBAAe,IAAI,mBAAmB;AAEtC,eAAO,WAAW,wBAAwB,cAAc;AACxD,eAAO,QAAQ,MAAM,EAAE,OAAQ;AAC/B,eAAO,IAAI,sBAAsB;AAEjC,sBAAc,SAAS,KAAK,IAAI;AAChC,sBAAc,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5B,sBAAc,GAAG,aAAa,cAAc;AAC5C,sBAAc,GAAG,QAAQ,MAAM;AAC/B,sBAAc,OAAO,MAAM;AAE3B,sBAAc,MAAM,OAAO;AAE3B,sBAAc,kBAAmB;AACjC,sBAAc,iBAAiB,KAAK,OAAO,gBAAgB;AAG3D,sBAAc,IAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;AAChG,sBAAc,SAAS,cAAc,gBAAgB;AACrD,sBAAc,SAAS,cAAc,kBAAkB;AACvD,sBAAc,SAAS,MAAM,WAAW;AAIxC,uBAAe,8BAA8B,QAAQ,sBAAsB;AAC3E,uBAAe,aAAa,cAAc,kBAAkB;AAE5D,kBAAU;AAAA,UACR,eAAe,OAAO;AAAA,UACtB,eAAe,OAAO;AAAA,UACtB,eAAe,OAAO;AAAA,UACtB,eAAe;AAAA,QAChB;AAED,cAAM,mBAAmB,cAAc;AAEvC,UAAE,KAAK,KAAK,KAAK,UAAU,CAAC,IAAI,iBAAiB,SAAS,CAAC,KAAK,iBAAiB,SAAS,CAAC;AAC3F,UAAE,KAAK,KAAK,KAAK,UAAU,CAAC,IAAI,iBAAiB,SAAS,CAAC,KAAK,iBAAiB,SAAS,CAAC;AAC3F,UAAE,IAAI;AACN,UAAE,KAAK,IAAM,iBAAiB,SAAS,EAAE,KAAK,iBAAiB,SAAS,EAAE;AAG1E,kBAAU,eAAe,IAAM,UAAU,IAAI,CAAC,CAAC;AAG/C,yBAAiB,SAAS,CAAC,IAAI,UAAU;AACzC,yBAAiB,SAAS,CAAC,IAAI,UAAU;AACzC,yBAAiB,SAAS,EAAE,IAAI,UAAU,IAAI,IAAM;AACpD,yBAAiB,SAAS,EAAE,IAAI,UAAU;AAG1C,cAAM,UAAU;AAEhB,cAAM,sBAAsB,SAAS,gBAAiB;AAEtD,cAAM,mBAAmB,SAAS,GAAG;AACrC,cAAM,0BAA0B,SAAS,UAAU;AACnD,cAAM,qBAAqB,SAAS;AAEpC,YAAI,SAAS;AACb,YAAI,sBAAsB;AAAU,mBAAS,SAAS,qBAAqB;AAAA;AACtE,mBAAS,SAAS,mBAAmB;AAE1C,iBAAS,GAAG,UAAU;AACtB,iBAAS,UAAU,aAAa;AAChC,YAAI,sBAAsB;AAAU,mBAAS,mBAAmB;AAAA;AAC3D,mBAAS,iBAAiB;AAC/B,iBAAS,cAAcC,MAAa;AAEpC,iBAAS,gBAAgB,YAAY;AAErC,iBAAS,MAAM,QAAQ,MAAM,QAAQ,IAAI;AAEzC,YAAI,SAAS,cAAc;AAAO,mBAAS,MAAO;AAClD,iBAAS,OAAO,OAAO,aAAa;AAEpC,iBAAS,GAAG,UAAU;AACtB,iBAAS,UAAU,aAAa;AAChC,iBAAS,cAAc;AAEvB,YAAI,sBAAsB;AAAU,mBAAS,mBAAmB,SAAS,SAAS;AAAA;AAC7E,mBAAS,iBAAiB,SAAS,OAAO;AAE/C,iBAAS,gBAAgB,mBAAmB;AAI5C,cAAM,WAAW,OAAO;AAExB,YAAI,aAAa,QAAW;AAC1B,mBAAS,MAAM,SAAS,QAAQ;AAAA,QACjC;AAED,cAAM,UAAU;AAAA,MACjB;AAED,WAAK,kBAAkB,WAAY;AACjC,eAAO;AAAA,MACR;AAED,WAAK,UAAU,WAAY;AACzB,qBAAa,QAAS;AACtB,cAAM,SAAS,QAAS;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AApPD,MAAMC,aAAN;AACE,gBADIA,YACG,mBAAkB;AAAA,IACvB,UAAU;AAAA,MACR,OAAO;AAAA,QACL,OAAO;AAAA,MACR;AAAA,MAED,UAAU;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MAED,eAAe;AAAA,QACb,OAAO;AAAA,MACR;AAAA,IACF;AAAA,IAED;AAAA;AAAA,MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAiBzB;AAAA;AAAA,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eA2BlBC,qBAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA,EAGnD;AAuLH,SAAOD;AACT,GAAC;;"}