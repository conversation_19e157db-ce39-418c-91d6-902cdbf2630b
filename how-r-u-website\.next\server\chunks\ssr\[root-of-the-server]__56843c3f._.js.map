{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/CustomCursor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport function CustomCursor() {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const updatePosition = (e: MouseEvent) => {\n      setPosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseEnter = () => setIsVisible(true);\n    const handleMouseLeave = () => setIsVisible(false);\n\n    document.addEventListener('mousemove', updatePosition);\n    document.addEventListener('mouseenter', handleMouseEnter);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    return () => {\n      document.removeEventListener('mousemove', updatePosition);\n      document.removeEventListener('mouseenter', handleMouseEnter);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n    };\n  }, []);\n\n  return (\n    <div\n      className={`custom-cursor ${isVisible ? 'opacity-100' : 'opacity-0'}`}\n      style={{\n        left: position.x - 10,\n        top: position.y - 10,\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,CAAC;YACtB,YAAY;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAC3C;QAEA,MAAM,mBAAmB,IAAM,aAAa;QAC5C,MAAM,mBAAmB,IAAM,aAAa;QAE5C,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,cAAc;QACxC,SAAS,gBAAgB,CAAC,cAAc;QAExC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,cAAc;YAC3C,SAAS,mBAAmB,CAAC,cAAc;QAC7C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,cAAc,EAAE,YAAY,gBAAgB,aAAa;QACrE,OAAO;YACL,MAAM,SAAS,CAAC,GAAG;YACnB,KAAK,SAAS,CAAC,GAAG;QACpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/DustParticles.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface Particle {\n  id: number;\n  x: number;\n  y: number;\n  delay: number;\n  size: number;\n}\n\nexport function DustParticles() {\n  const [particles, setParticles] = useState<Particle[]>([]);\n\n  useEffect(() => {\n    const generateParticles = () => {\n      const newParticles: Particle[] = [];\n      for (let i = 0; i < 20; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * window.innerWidth,\n          y: window.innerHeight + Math.random() * 100,\n          delay: Math.random() * 20,\n          size: Math.random() * 3 + 1,\n        });\n      }\n      setParticles(newParticles);\n    };\n\n    generateParticles();\n    window.addEventListener('resize', generateParticles);\n\n    return () => window.removeEventListener('resize', generateParticles);\n  }, []);\n\n  return (\n    <div className=\"fixed inset-0 pointer-events-none z-0\">\n      {particles.map((particle) => (\n        <div\n          key={particle.id}\n          className=\"dust-particle absolute\"\n          style={{\n            left: particle.x,\n            bottom: 0,\n            animationDelay: `${particle.delay}s`,\n            width: particle.size,\n            height: particle.size,\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,MAAM,eAA2B,EAAE;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,aAAa,IAAI,CAAC;oBAChB,IAAI;oBACJ,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;oBACpC,GAAG,OAAO,WAAW,GAAG,KAAK,MAAM,KAAK;oBACxC,OAAO,KAAK,MAAM,KAAK;oBACvB,MAAM,KAAK,MAAM,KAAK,IAAI;gBAC5B;YACF;YACA,aAAa;QACf;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,MAAM,SAAS,CAAC;oBAChB,QAAQ;oBACR,gBAAgB,GAAG,SAAS,KAAK,CAAC,CAAC,CAAC;oBACpC,OAAO,SAAS,IAAI;oBACpB,QAAQ,SAAS,IAAI;gBACvB;eARK,SAAS,EAAE;;;;;;;;;;AAa1B", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/DigitalRain.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\nexport function DigitalRain() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Matrix characters (including some Lumon-style corporate text)\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789LUMON_KIER_MDR_WELLNESS_PROTOCOL_BREACH';\n    const charArray = chars.split('');\n\n    const fontSize = 14;\n    const columns = canvas.width / fontSize;\n    const drops: number[] = [];\n\n    // Initialize drops\n    for (let i = 0; i < columns; i++) {\n      drops[i] = 1;\n    }\n\n    const draw = () => {\n      // Semi-transparent black background for trail effect\n      ctx.fillStyle = 'rgba(10, 10, 10, 0.05)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Set text properties\n      ctx.fillStyle = '#00ff41'; // Terminal green\n      ctx.font = `${fontSize}px 'IBM Plex Mono', monospace`;\n\n      // Draw characters\n      for (let i = 0; i < drops.length; i++) {\n        const text = charArray[Math.floor(Math.random() * charArray.length)];\n        const x = i * fontSize;\n        const y = drops[i] * fontSize;\n\n        // Add some randomness to the glow effect\n        if (Math.random() > 0.98) {\n          ctx.shadowColor = '#00ff41';\n          ctx.shadowBlur = 10;\n        } else {\n          ctx.shadowBlur = 0;\n        }\n\n        ctx.fillText(text, x, y);\n\n        // Reset drop to top randomly or when it reaches bottom\n        if (y > canvas.height && Math.random() > 0.975) {\n          drops[i] = 0;\n        }\n\n        drops[i]++;\n      }\n    };\n\n    // Animation loop\n    const interval = setInterval(draw, 50);\n\n    return () => {\n      clearInterval(interval);\n      window.removeEventListener('resize', resizeCanvas);\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"fixed inset-0 pointer-events-none z-0 opacity-20\"\n      style={{ background: 'transparent' }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,kBAAkB;QAClB,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QACpC;QACA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,gEAAgE;QAChE,MAAM,QAAQ;QACd,MAAM,YAAY,MAAM,KAAK,CAAC;QAE9B,MAAM,WAAW;QACjB,MAAM,UAAU,OAAO,KAAK,GAAG;QAC/B,MAAM,QAAkB,EAAE;QAE1B,mBAAmB;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAChC,KAAK,CAAC,EAAE,GAAG;QACb;QAEA,MAAM,OAAO;YACX,qDAAqD;YACrD,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE9C,sBAAsB;YACtB,IAAI,SAAS,GAAG,WAAW,iBAAiB;YAC5C,IAAI,IAAI,GAAG,GAAG,SAAS,6BAA6B,CAAC;YAErD,kBAAkB;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;gBACpE,MAAM,IAAI,IAAI;gBACd,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG;gBAErB,yCAAyC;gBACzC,IAAI,KAAK,MAAM,KAAK,MAAM;oBACxB,IAAI,WAAW,GAAG;oBAClB,IAAI,UAAU,GAAG;gBACnB,OAAO;oBACL,IAAI,UAAU,GAAG;gBACnB;gBAEA,IAAI,QAAQ,CAAC,MAAM,GAAG;gBAEtB,uDAAuD;gBACvD,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO;oBAC9C,KAAK,CAAC,EAAE,GAAG;gBACb;gBAEA,KAAK,CAAC,EAAE;YACV;QACF;QAEA,iBAAiB;QACjB,MAAM,WAAW,YAAY,MAAM;QAEnC,OAAO;YACL,cAAc;YACd,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,YAAY;QAAc;;;;;;AAGzC", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}