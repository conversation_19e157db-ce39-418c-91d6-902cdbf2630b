"use client";

import { useEffect, useState } from "react";

interface Particle {
  id: number;
  x: number;
  y: number;
  delay: number;
  size: number;
  opacity: number;
  speed: number;
}

export function DustParticles() {
  const [particles, setParticles] = useState<Particle[]>([]);

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      for (let i = 0; i < 30; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * window.innerWidth,
          y: window.innerHeight + Math.random() * 100,
          delay: Math.random() * 25,
          size: Math.random() * 4 + 1,
          opacity: Math.random() * 0.3 + 0.1,
          speed: Math.random() * 15 + 10,
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
    window.addEventListener("resize", generateParticles);

    return () => window.removeEventListener("resize", generateParticles);
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full bg-white animate-ethereal-drift"
          style={{
            left: particle.x,
            bottom: 0,
            animationDelay: `${particle.delay}s`,
            animationDuration: `${particle.speed}s`,
            width: particle.size,
            height: particle.size,
            opacity: particle.opacity,
            boxShadow: `0 0 ${particle.size * 2}px rgba(255, 255, 255, ${
              particle.opacity
            })`,
          }}
        />
      ))}
    </div>
  );
}
